﻿using System;
using Autodesk.Forge;
using Autodesk.Forge.Model;

namespace Struxit2._0.Services
{
    public class Tokens
    {
        public string InternalToken { get; set; }
        public string PublicToken { get; set; }
        public string RefreshToken { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public partial class APS
    {
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _callbackUri;
        private readonly Scope[] InternalTokenScopes = new Scope[] { Scope.DataRead, Scope.DataWrite, Scope.BucketCreate, Scope.BucketRead, Scope.AccountRead };
        //private readonly Scope[] PublicTokenScopes = new Scope[] { Scope.ViewablesRead };
        private readonly Scope[] PublicTokenScopes = new Scope[] { Scope.DataRead, Scope.DataWrite, Scope.BucketCreate, Scope.BucketRead, Scope.AccountRead };


        public APS(string clientId, string clientSecret, string callbackUri)
        {
            _clientId = clientId;
            _clientSecret = clientSecret;
            _callbackUri = callbackUri;
        }
    }
}