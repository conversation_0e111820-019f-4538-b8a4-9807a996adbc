﻿@using System
@using Struxit2._0.Model
@using System.Text.RegularExpressions

@{
    ViewBag.Title = "AllPdfs";
    Layout = "~/Views/Shared/_layout1.cshtml";

    Func<string, SheetDetails> extractSheetNameAndDescriptionRegular = (fileName) =>
    {
        string sheetName = string.Empty;
        string description = string.Empty;

        // Remove the ".pdf" extension if it exists
        if (fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
        {
            fileName = fileName.Substring(0, fileName.Length - 4).Trim();
        }

        // Regular expression to match the sheet name
        string pattern = @"(?<sheetName>\d{4}[-_]\d{4}[-_][A-Z]-\d{4})";
        string pattern2 = @"(?<sheetName>\d{4}[-_]\d{4}[_][A-Z]_\d{4})";
        string pattern3 = @"^(?<sheetName>\d{4}[-_]\d{4}[_][A-Z][_]\d{4})\s+(?<revision>REV\d{2})\s+(?<description>.*)$";
        string pattern4 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})";
        string pattern5 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4})\s+\((?<description>.+)\)";
        string pattern6 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4})\s+\((?<description>.+)\)";
        string pattern7 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[A-Z]{2}[-]\d{4})\s+-\s+(?<description>.+)";
        string pattern8 = @"(?<sheetName>\d{4}[-]\d{3}[-]\s[BS\d{4}]+)\s+-\s+\(\s*(?<description>.+?)\s*\)";
        string pattern9 = @"(?<sheetName>\d{4}-\d{3}-\sBS\d{4})\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)-\d+";
        string pattern10 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+?)\s+\(Rev\s+\d+\)";
        string pattern11 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}-\d)\s+-\s+(?<description>.+)";
        string pattern12 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4})\s+(?<description>.+)";
        string pattern13 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4}[A-Z]?)\s+-\s+\(\s*(?<description>.+?)\s*\)\s+-\s+REV\s+\d+";
        string pattern14 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+)";
        string pattern15 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}\.\d)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)";
        string pattern16 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}\.\d)\s+-\s+REV\s+\d+\s+\(\s*(?<description>.+?)\s*\)";
        string pattern17 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]+\d{3})\s+-\s+REV\s+\d+\s+\(\s*(?<description>.+?)\s*\)";
        string pattern18 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4}-)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)";
        string pattern19 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4})\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)";
        string pattern20 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4}-)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)";
        string pattern21 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4})\s+-\s+\(\s*(?<description>.+?)\s*\)";
        string pattern22 = @"^(?<sheetName>\d{4}_\d{4}-[A-Z]-\d+-\d+)\s.*?\s(?<description>.*)$";
        string pattern23 = @"^(?<sheetName>[A-Z]_\d+)$";
        string pattern24 = @"^(?<sheetName>\d{3}-[A-Z])\sREV\s\d+\s-\s(?<description>.*)$";
        string pattern25 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR-\d{4})-\sREV\s\d+\s-\s\((?<description>.*)\)$";
        string pattern26 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})\s-\sREV\s\d+\s\((?<description>.*)\)$";
        string pattern27 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s\((?<description>.*)\)$";
        string pattern28 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s-\s\((?<description>.*)\)$";
        string pattern29 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s\((?<description>.*)\)$";
        string pattern30 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s\((?<description>.*)\)$";
        string pattern31 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s-\((?<description>.*)\)$";
        string pattern32 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})\s-\sREV\s\d+\s-\((?<description>.*)\)$";
        string pattern33 = @"^(?<sheetName>\d{4}[-_]\d{4}[-_][A-Z]-\d{4}[-]\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})";
        string pattern34 = @"^(?<sheetName>\d{4}[-_]\d{4}[-_][A-Z]-\d{4}[-_][A-Z]-\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})";
        string pattern35 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{3}[_][A-Z])\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})";
        string pattern36 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4}[_][A-Z])\s+\((?<description>.+)\)";
        string pattern37 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4}[-][A-Z])\s+\((?<description>.+)\)";
        string pattern38 = @"^(?<sheetName>\d{4}[-]\d{3}[-]\s[BS\d{4}]+[-][A-Z])\s+-\s+\(\s*(?<description>.+?)\s*\)";
        string pattern39 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})\s-\sREV\s\d+\s-\s\((?<description>.*)\)$";
        string pattern40 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}-[A-Z]-\d{4})-R\d+\s(?<description>.*)$";
        string pattern41 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-]R\d+\s(?<description>.*)$";
        string pattern42 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-]REV\d+\s(?<description>.*)$";
        string pattern43 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-]Rev\d+\s(?<description>.*)$";
        string pattern44 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-][Rr]ev[_ ]\d+\s(?<description>.*)$";
        string pattern45 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-][Rr]\d+\s(?<description>.*)$";
        string pattern46 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-][Rr]\d+[_ ]?\s(?<description>.*)$";
        string pattern47 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-][Rr][eE][vV]\s?\d+\s(?<description>.*)$";
        string pattern48 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][_]\d{3})[_-][Rr][eE][vV]\s?\d+\s(?<description>.*)$";
        string pattern49 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][_]\d{2})[_-][Rr][eE][vV]\s?\d+\s(?<description>.*)$";
        string pattern50 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][_]\d{3}[_-][A-Z])[_-][Rr][eE][vV]\s?\d+\s(?<description>.*)$";
        string pattern51 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}-[A-Z]-\d{4})_(?<description>.+)_REV\s?(?<revision>\d+)_V(?<version>\d+)\sPrint$";
        string pattern52 = @"^(?<sheetName>JAC\sTEST)$";
        string pattern53 = @"^(?<sheetName>\d{4}\s-\s\d{4}-[A-Z]-\d{4})-REV\s\d+\s?-\s?(?<description>.*)$";
        string pattern54 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}[A-Z]-R-\d{4})\s-\s\((?<description>.*?)\)\s-\sREV\s?\d+$";
        string pattern55 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}[A-Z]-R-\d{4})\s-\s\((?<description>.*?)\)\s-\sREV\s?\d+$";
        string pattern56 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}[A-Z]-R-\d{4})\s-\s\((?<description>.*?)\)\s-\sREV\s?\d+$";

        string pattern57 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z]-R-\d{4})[\s-]\((?<description>.*?)\)[\s-][Rr][\s-]?\d+$";
        string pattern58 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z][\s-][A-Z]-\d{4})[\s-]\((?<description>.*?)\)[\s-]REV[\s-]?\d+$";
        string pattern59 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z][\s-][A-Z]-\d{4})[\s-]\((?<description>.*?)\)[\s-][Rr][Ee][Vv][\s-]?\d+$";
        string pattern60 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z][\s-][A-Z]-\d{4})[\s-]\((?<description>.*?)\)[\s-][Rr][\s-]?\d+$";
        string pattern61 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][\s-]\d{3})[\s-]\((?<description>.*?)\)[\s-][Rr][Ee][Vv][\s-]?\d+$";
        string pattern62 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][\s-]\d{2})[\s-]\((?<description>.*?)\)[\s-][Rr][Ee][Vv][\s-]?\d+$";
        string pattern63 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}[A-Z]-[A-Z]-\d{4})\s-\s\((?<description>.*?)\)\s-\sREV\s?\d+$";
        string pattern64 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+)$";
        string pattern65 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+?)(?:\s+-\s+REV\s?\d+)?$";
        string pattern66 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+\((?<description>.+?)\)\s+-\s+REV\s?\d+$";
        string pattern67 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+?)\s+-\s+REV\s?\d+$";
        string pattern68 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z]-\d{4})$";
        string pattern69 = @"^(?<sheetName>\d{4}[_-]\d{4}[_][A-Z]_\d{4})$";
        string pattern70 = @"^(?<sheetName>\d{4}[_-]\d{4}[_][A-Z][_]\d{4})\s+(?<revision>REV\d{2})\s+(?<description>.*)$";
        string pattern71 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})$";
        string pattern72 = @"^(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4})\s+\((?<description>.+)\)$";
        string pattern73 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4})\s+\((?<description>.+)\)$";
        string pattern74 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[A-Z]{2}[-]\d{4})\s+-\s+(?<description>.+)$";
        string pattern75 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\s?\d+)?$";
        string pattern76 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+\((?<description>.+?)\)\s+-\s+(?<revision>REV\s?\d+)?$";
        string pattern77 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+?)\s+-\s+REV\s?\d+$";
        string pattern78 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z][_]\d{4})[_-]REV?\d+\s?(?<description>.*)?$";
        string pattern79 = @"^(?<sheetName>\d{4}[_-]\d{4}[-]\d{2}[-][A-Z]-\d{4}\.\d+)\((?<description>.*)\)$";





        var match = Regex.Match(fileName, pattern);
        var match2 = Regex.Match(fileName, pattern2);
        var match3 = Regex.Match(fileName, pattern3);
        var match4 = Regex.Match(fileName, pattern4);
        var match5 = Regex.Match(fileName, pattern5);
        var match6 = Regex.Match(fileName, pattern6);
        var match7 = Regex.Match(fileName, pattern7);
        var match8 = Regex.Match(fileName, pattern8);
        var match9 = Regex.Match(fileName, pattern9);
        var match10 = Regex.Match(fileName, pattern10);
        var match11 = Regex.Match(fileName, pattern11);
        var match12 = Regex.Match(fileName, pattern12);
        var match13 = Regex.Match(fileName, pattern13);
        var match14 = Regex.Match(fileName, pattern14);
        var match15 = Regex.Match(fileName, pattern15);
        var match16 = Regex.Match(fileName, pattern16);
        var match17 = Regex.Match(fileName, pattern17);
        var match18 = Regex.Match(fileName, pattern18);
        var match19 = Regex.Match(fileName, pattern19);
        var match20 = Regex.Match(fileName, pattern20);
        var match21 = Regex.Match(fileName, pattern21);
        var match22 = Regex.Match(fileName, pattern22);
        var match23 = Regex.Match(fileName, pattern23);
        var match24 = Regex.Match(fileName, pattern24);
        var match25 = Regex.Match(fileName, pattern25);
        var match26 = Regex.Match(fileName, pattern26);
        var match27 = Regex.Match(fileName, pattern27);
        var match28 = Regex.Match(fileName, pattern28);
        var match29 = Regex.Match(fileName, pattern29);
        var match30 = Regex.Match(fileName, pattern30);
        var match31 = Regex.Match(fileName, pattern31);
        var match32 = Regex.Match(fileName, pattern32);
        var match33 = Regex.Match(fileName, pattern33);
        var match34 = Regex.Match(fileName, pattern34);
        var match35 = Regex.Match(fileName, pattern35);
        var match36 = Regex.Match(fileName, pattern36);
        var match37 = Regex.Match(fileName, pattern37);
        var match38 = Regex.Match(fileName, pattern38);
        var match39 = Regex.Match(fileName, pattern39);
        var match40 = Regex.Match(fileName, pattern40);
        var match41 = Regex.Match(fileName, pattern41);
        var match42 = Regex.Match(fileName, pattern42);
        var match43 = Regex.Match(fileName, pattern43);
        var match44 = Regex.Match(fileName, pattern44);
        var match45 = Regex.Match(fileName, pattern45);
        var match46 = Regex.Match(fileName, pattern46);
        var match47 = Regex.Match(fileName, pattern47);
        var match48 = Regex.Match(fileName, pattern48);
        var match49 = Regex.Match(fileName, pattern49);
        var match50 = Regex.Match(fileName, pattern50);
        var match51 = Regex.Match(fileName, pattern51);
        var match52 = Regex.Match(fileName, pattern52);
        var match53 = Regex.Match(fileName, pattern53);
        var match54 = Regex.Match(fileName, pattern54);
        var match55 = Regex.Match(fileName, pattern55);
        var match56 = Regex.Match(fileName, pattern56);
        var match57 = Regex.Match(fileName, pattern57);
        var match58 = Regex.Match(fileName, pattern58);
        var match59 = Regex.Match(fileName, pattern59);
        var match60 = Regex.Match(fileName, pattern60);
        var match61 = Regex.Match(fileName, pattern61);
        var match62 = Regex.Match(fileName, pattern62);
        var match63 = Regex.Match(fileName, pattern63);
        var match64 = Regex.Match(fileName, pattern64);
        var match65 = Regex.Match(fileName, pattern65);
        var match66 = Regex.Match(fileName, pattern66);
        var match67 = Regex.Match(fileName, pattern67);
        var match68 = Regex.Match(fileName, pattern68);
        var match69 = Regex.Match(fileName, pattern69);
        var match70 = Regex.Match(fileName, pattern70);
        var match71 = Regex.Match(fileName, pattern71);
        var match72 = Regex.Match(fileName, pattern72);
        var match73 = Regex.Match(fileName, pattern73);
        var match74 = Regex.Match(fileName, pattern74);
        var match75 = Regex.Match(fileName, pattern75);
        var match76 = Regex.Match(fileName, pattern76);
        var match77 = Regex.Match(fileName, pattern77);
        var match78 = Regex.Match(fileName, pattern78);
        var match79 = Regex.Match(fileName, pattern79);


        if (match.Success)
        {
            sheetName = match.Groups["sheetName"].Value;

            int sheetNameIndex = fileName.IndexOf(sheetName) + sheetName.Length;
            description = fileName.Substring(sheetNameIndex).Trim();
        }
        else if (match2.Success)
        {
            sheetName = match2.Groups["sheetName"].Value;

            int sheetNameIndex = fileName.IndexOf(sheetName) + sheetName.Length;
            description = fileName.Substring(sheetNameIndex).Trim();
        }
        else if (match3.Success)
        {
            sheetName = match3.Groups["sheetName"].Value;

            int sheetNameIndex = fileName.IndexOf(sheetName) + sheetName.Length;
            description = fileName.Substring(sheetNameIndex).Trim();
        }
        else if (match4.Success)
        {
            sheetName = match4.Groups["sheetName"].Value;

            int sheetNameIndex = fileName.IndexOf(sheetName) + sheetName.Length;
            description = fileName.Substring(sheetNameIndex).Trim();
        }
        else if (match5.Success)
        {
            sheetName = match5.Groups["sheetName"].Value;
            description = match5.Groups["description"].Value;
        }
        else if (match6.Success)
        {
            sheetName = match6.Groups["sheetName"].Value;
            description = match6.Groups["description"].Value;
        }
        else if (match7.Success)
        {
            sheetName = match7.Groups["sheetName"].Value;
            description = match7.Groups["description"].Value;
        }
        else if (match8.Success)
        {
            sheetName = match8.Groups["sheetName"].Value;
            description = match8.Groups["description"].Value;
        }
        else if (match9.Success)
        {
            sheetName = match9.Groups["sheetName"].Value;
            description = match9.Groups["description"].Value;
        }
        else if (match10.Success)
        {
            sheetName = match10.Groups["sheetName"].Value;
            description = match10.Groups["description"].Value;
        }
        else if (match11.Success)
        {
            sheetName = match11.Groups["sheetName"].Value;
            description = match11.Groups["description"].Value;
        }
        else if (match12.Success)
        {
            sheetName = match12.Groups["sheetName"].Value;
            description = match12.Groups["description"].Value;
        }
        else if (match13.Success)
        {
            sheetName = match13.Groups["sheetName"].Value;
            description = match13.Groups["description"].Value;
        }
        else if (match14.Success)
        {
            sheetName = match14.Groups["sheetName"].Value;
            description = match14.Groups["description"].Value;
        }
        else if (match15.Success)
        {
            sheetName = match15.Groups["sheetName"].Value;
            description = match15.Groups["description"].Value;
        }
        else if (match16.Success)
        {
            sheetName = match16.Groups["sheetName"].Value;
            description = match16.Groups["description"].Value;
        }
        else if (match17.Success)
        {
            sheetName = match17.Groups["sheetName"].Value;
            description = match17.Groups["description"].Value;
        }
        else if (match18.Success)
        {
            sheetName = match18.Groups["sheetName"].Value;
            description = match18.Groups["description"].Value;
        }
        else if (match19.Success)
        {
            sheetName = match19.Groups["sheetName"].Value;
            description = match19.Groups["description"].Value;
        }
        else if (match20.Success)
        {
            sheetName = match20.Groups["sheetName"].Value;
            description = match20.Groups["description"].Value;
        }
        else if (match21.Success)
        {
            sheetName = match21.Groups["sheetName"].Value;
            description = match21.Groups["description"].Value;
        }
        else if (match22.Success)
        {
            sheetName = match22.Groups["sheetName"].Value;
            description = match22.Groups["description"].Value;
        }
        else if (match23.Success)
        {
            sheetName = match23.Groups["sheetName"].Value;
            description = "";
        }
        else if (match24.Success)
        {
            sheetName = match24.Groups["sheetName"].Value;
            description = match24.Groups["description"].Value;
        }
        else if (match25.Success)
        {
            sheetName = match25.Groups["sheetName"].Value;
            description = match25.Groups["description"].Value;
        }
        else if (match26.Success)
        {
            sheetName = match26.Groups["sheetName"].Value;
            description = match26.Groups["description"].Value;
        }
        else if (match27.Success)
        {
            sheetName = match27.Groups["sheetName"].Value;
            description = match27.Groups["description"].Value;
        }
        else if (match28.Success)
        {
            sheetName = match28.Groups["sheetName"].Value;
            description = match28.Groups["description"].Value;
        }
        else if (match29.Success)
        {
            sheetName = match29.Groups["sheetName"].Value;
            description = match29.Groups["description"].Value;
        }
        else if (match30.Success)
        {
            sheetName = match30.Groups["sheetName"].Value;
            description = match30.Groups["description"].Value;
        }
        else if (match31.Success)
        {
            sheetName = match31.Groups["sheetName"].Value;
            description = match31.Groups["description"].Value;
        }
        else if (match32.Success)
        {
            sheetName = match32.Groups["sheetName"].Value;
            description = match32.Groups["description"].Value;
        }
        else if (match33.Success)
        {
            sheetName = match33.Groups["sheetName"].Value;
            description = match33.Groups["description"].Value;
        }
        else if (match34.Success)
        {
            sheetName = match34.Groups["sheetName"].Value;
            description = match34.Groups["description"].Value;
        }
        else if (match35.Success)
        {
            sheetName = match35.Groups["sheetName"].Value;
            description = match35.Groups["description"].Value;
        }
        else if (match36.Success)
        {
            sheetName = match36.Groups["sheetName"].Value;
            description = match36.Groups["description"].Value;
        }
        else if (match37.Success)
        {
            sheetName = match37.Groups["sheetName"].Value;
            description = match37.Groups["description"].Value;
        }
        else if (match38.Success)
        {
            sheetName = match38.Groups["sheetName"].Value;
            description = match38.Groups["description"].Value;
        }
        else if (match39.Success)
        {
            sheetName = match39.Groups["sheetName"].Value;
            description = match39.Groups["description"].Value;
        }
        else if (match40.Success)
        {
            sheetName = match40.Groups["sheetName"].Value;
            description = match40.Groups["description"].Value;
        }
        else if (match41.Success)
        {
            sheetName = match41.Groups["sheetName"].Value;
            description = match41.Groups["description"].Value;
        }
        else if (match42.Success)
        {
            sheetName = match42.Groups["sheetName"].Value;
            description = match42.Groups["description"].Value;
        }
        else if (match43.Success)
        {
            sheetName = match43.Groups["sheetName"].Value;
            description = match43.Groups["description"].Value;
        }
        else if (match44.Success)
        {
            sheetName = match44.Groups["sheetName"].Value;
            description = match44.Groups["description"].Value;
        }
        else if (match45.Success)
        {
            sheetName = match45.Groups["sheetName"].Value;
            description = match45.Groups["description"].Value;
        }
        else if (match46.Success)
        {
            sheetName = match46.Groups["sheetName"].Value;
            description = match46.Groups["description"].Value;
        }
        else if (match47.Success)
        {
            sheetName = match47.Groups["sheetName"].Value;
            description = match47.Groups["description"].Value;
        }
        else if (match48.Success)
        {
            sheetName = match48.Groups["sheetName"].Value;
            description = match48.Groups["description"].Value;
        }
        else if (match49.Success)
        {
            sheetName = match49.Groups["sheetName"].Value;
            description = match49.Groups["description"].Value;
        }
        else if (match50.Success)
        {
            sheetName = match50.Groups["sheetName"].Value;
            description = match50.Groups["description"].Value;
        }
        else if (match51.Success)
        {
            sheetName = match51.Groups["sheetName"].Value;
            description = match51.Groups["description"].Value;
        }
        else if (match52.Success)
        {
            sheetName = match52.Groups["sheetName"].Value;
            description = match52.Groups["description"].Value;
        }
        else if (match53.Success)
        {
            sheetName = match53.Groups["sheetName"].Value;
            description = match53.Groups["description"].Value;
        }
        else if (match54.Success)
        {
            sheetName = match54.Groups["sheetName"].Value;
            description = match54.Groups["description"].Value;
        }
        else if (match55.Success)
        {
            sheetName = match55.Groups["sheetName"].Value;
            description = match55.Groups["description"].Value;
        }
        else if (match56.Success)
        {
            sheetName = match56.Groups["sheetName"].Value;
            description = match56.Groups["description"].Value;
        }
        else if (match57.Success)
        {
            sheetName = match57.Groups["sheetName"].Value;
            description = match57.Groups["description"].Value;
        }
        else if (match58.Success)
        {
            sheetName = match58.Groups["sheetName"].Value;
            description = match58.Groups["description"].Value;
        }
        else if (match59.Success)
        {
            sheetName = match59.Groups["sheetName"].Value;
            description = match59.Groups["description"].Value;
        }
        else if (match60.Success)
        {
            sheetName = match60.Groups["sheetName"].Value;
            description = match60.Groups["description"].Value;
        }
        else if (match61.Success)
        {
            sheetName = match61.Groups["sheetName"].Value;
            description = match61.Groups["description"].Value;
        }
        else if (match62.Success)
        {
            sheetName = match62.Groups["sheetName"].Value;
            description = match62.Groups["description"].Value;
        }
        else if (match63.Success)
        {
            sheetName = match63.Groups["sheetName"].Value;
            description = match63.Groups["description"].Value;
        }
        else if (match64.Success)
        {
            sheetName = match64.Groups["sheetName"].Value;
            description = match64.Groups["description"].Value;
        }
        else if (match65.Success)
        {
            sheetName = match65.Groups["sheetName"].Value;
            description = match65.Groups["description"].Value;
        }
        else if (match66.Success)
        {
            sheetName = match66.Groups["sheetName"].Value;
            description = match66.Groups["description"].Value;
        }
        else if (match67.Success)
        {
            sheetName = match67.Groups["sheetName"].Value;
            description = match67.Groups["description"].Value;
        }
        else if (match68.Success)
        {
            sheetName = match68.Groups["sheetName"].Value;
            description = match68.Groups["description"].Value;
        }
        else if (match69.Success)
        {
            sheetName = match69.Groups["sheetName"].Value;
            description = match69.Groups["description"].Value;
        }
        else if (match70.Success)
        {
            sheetName = match70.Groups["sheetName"].Value;
            description = match70.Groups["description"].Value;
        }
        else if (match71.Success)
        {
            sheetName = match71.Groups["sheetName"].Value;
            description = match71.Groups["description"].Value;
        }
        else if (match72.Success)
        {
            sheetName = match72.Groups["sheetName"].Value;
            description = match72.Groups["description"].Value;
        }
        else if (match73.Success)
        {
            sheetName = match73.Groups["sheetName"].Value;
            description = match73.Groups["description"].Value;
        }
        else if (match74.Success)
        {
            sheetName = match74.Groups["sheetName"].Value;
            description = match74.Groups["description"].Value;
        }
        else if (match75.Success)
        {
            sheetName = match75.Groups["sheetName"].Value;
            description = match75.Groups["description"].Value;
        }
        else if (match76.Success)
        {
            sheetName = match76.Groups["sheetName"].Value;
            description = match76.Groups["description"].Value;
        }
        else if (match77.Success)
        {
            sheetName = match77.Groups["sheetName"].Value;
            description = match77.Groups["description"].Value;
        }
        else if (match78.Success)
        {
            sheetName = match78.Groups["sheetName"].Value;
            description = match78.Groups["description"].Value;
        }
        else if (match79.Success)
        {
            sheetName = match79.Groups["sheetName"].Value;
            description = match79.Groups["description"].Value;
        }
        else
        {
            string[] parts = fileName.Split('-');

            // If the parts are bigger Than three
            if (parts.Length > 4)
            {
                sheetName = string.Join("-", parts.Take(4)).Trim();
                description = string.Join("-", parts.Skip(4)).Trim();
            }
            else if (parts.Length > 3 && parts[3].Contains("("))
            {
                sheetName = string.Join("-", parts.Take(3)).Trim();
                description = string.Join("-", parts.Skip(3)).Trim();
            }
            else
            {
                sheetName = fileName;
                description = string.Empty;
            }
        }

        // Clean up the description by removing unnecessary parts
        // Handle parentheses in description
        int parenthesesIndex = description.IndexOf('(');
        if (parenthesesIndex != -1)
        {
            description = description.Substring(parenthesesIndex + 1);
            int endParenthesesIndex = description.IndexOf(')');
            if (endParenthesesIndex != -1)
            {
                description = description.Substring(0, endParenthesesIndex).Trim();
            }
        }

        // Remove trailing "REV" information from the description if it exists
        const string revSeparator = "REV ";
        int revIndex = description.LastIndexOf(revSeparator, StringComparison.OrdinalIgnoreCase);
        if (revIndex != -1)
        {
            description = description.Substring(0, revIndex).Trim();
        }

        // Remove leading/trailing underscores, dashes, or spaces
        description = RemoveHyphenAndSpace(description.Trim('-', '_', ' '));

        //Loop through to check if it has the same name or the name exists

        return new SheetDetails
        {
            SheetName = RemoveRev(sheetName.Trim().Trim(' ').Replace(" ", "").Trim('.').Trim('-')),
            Description = RemoveRev(description.Trim().Trim(' ').Trim('.').Trim('-').TrimStart('-', ' ').TrimEnd('-', ' '))
        };
    };

    string RemoveHyphenAndSpace(string input)
    {
        string pattern = " - ";
        int index = input.IndexOf(pattern);
        if (index >= 0)
        {
            // Remove the pattern
            return input.Remove(index, pattern.Length);
        }
        return input;
    }

    Func<string, string, SheetDetails> extractSheetNameAndDescriptionRegular1 = (fileName, projectName) =>
    {
        string sheetName = string.Empty;
        string description = string.Empty;
        string projectNumber = string.Empty;

        // Remove the ".pdf" extension if it exists
        if (fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
        {
            fileName = fileName.Substring(0, fileName.Length - 4).Trim();
        }

        // Regular expression to match the project number and name
        string projectPattern = @"^(?<projectNumber>\d{4}[_-]\d{4})\s+-\s+(?<projectName>.*)";
        Match projectMatch = Regex.Match(fileName, projectPattern);

        if (projectMatch.Success)
        {
            projectNumber = projectMatch.Groups["projectNumber"].Value;
            projectName = projectMatch.Groups["projectName"].Value;

            // Remove the project number and name from the file name for further processing
            fileName = fileName.Substring(projectMatch.Length).Trim();
        }

        // Regular expression to match the sheet name and description
        string pattern = @"(?<sheetName>\d{4}[-_]\d{4}[-_][A-Z]-\d{4})";
        string pattern2 = @"(?<sheetName>\d{4}[-_]\d{4}[_][A-Z]_\d{4})";
        string pattern3 = @"^(?<sheetName>\d{4}[-_]\d{4}[_][A-Z][_]\d{4})\s+(?<revision>REV\d{2})\s+(?<description>.*)$";
        string pattern4 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})";
        string pattern5 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4})\s+\((?<description>.+)\)";
        string pattern6 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4})\s+\((?<description>.+)\)";
        string pattern7 = @"(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[A-Z]{2}[-]\d{4})\s+-\s+(?<description>.+)$";
        string pattern8 = @"(?<sheetName>\d{4}[-]\d{3}[-]\s[BS\d{4}]+)\s+-\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern9 = @"(?<sheetName>\d{4}-\d{3}-\sBS\d{4})\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)-\d+";
        string pattern10 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+?)\s+\(Rev\s+\d+\)$";
        string pattern11 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}-\d)\s+-\s+(?<description>.+)$";
        string pattern12 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4})\s+(?<description>.+)$";
        string pattern13 = @"(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4}[A-Z]?)\s+-\s+\(\s*(?<description>.+?)\s*\)\s+-\s+REV\s+\d+$";
        string pattern14 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+)$";
        string pattern15 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}\.\d)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern16 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+BS\d{4}\.\d)\s+-\s+REV\s+\d+\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern17 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]+\d{3})\s+-\s+REV\s+\d+\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern18 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4}-)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern19 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4})\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern20 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4}-)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern21 = @"(?<sheetName>\d{2}-\d{3}\s+-\s+[A-Z]-\d{4})\s+-\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern22 = @"^(?<sheetName>\d{4}_\d{4}-[A-Z]-\d+-\d+)\s.*?\s(?<description>.*)$";
        string pattern23 = @"^(?<sheetName>[A-Z]_\d+)$";
        string pattern24 = @"^(?<sheetName>\d{3}-[A-Z])\sREV\s\d+\s-\s(?<description>.*)$";
        string pattern25 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR-\d{4})-\sREV\s\d+\s-\s\((?<description>.*)\)$";
        string pattern26 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})\s-\sREV\s\d+\s\((?<description>.*)\)$";
        string pattern27 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s\((?<description>.*)\)$";
        string pattern28 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s-\s\((?<description>.*)\)$";
        string pattern29 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s\((?<description>.*)\)$";
        string pattern30 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s\((?<description>.*)\)$";
        string pattern31 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})-REV\s\d+\s-\((?<description>.*)\)$";
        string pattern32 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})\s-\sREV\s\d+\s-\((?<description>.*)\)$";
        string pattern33 = @"^(?<sheetName>\d{4}[-_]\d{4}[-_][A-Z]-\d{4}[-]\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})$";
        string pattern34 = @"^(?<sheetName>\d{4}[-_]\d{4}[-_][A-Z]-\d{4}[-_][A-Z]-\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})$";
        string pattern35 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{3}[_][A-Z])\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})$";
        string pattern36 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4}[_][A-Z])\s+\((?<description>.+)\)$";
        string pattern37 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4}[-][A-Z])\s+\((?<description>.+)\)$";
        string pattern38 = @"^(?<sheetName>\d{4}[-]\d{3}[-]\s[BS\d{4}]+[-][A-Z])\s+-\s+\(\s*(?<description>.+?)\s*\)$";
        string pattern39 = @"^(?<sheetName>\d{2}-\d{3}\s-\sR\d{4})\s-\sREV\s\d+\s-\s\((?<description>.*)\)$";
        string pattern40 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}-[A-Z]-\d{4})-R\d+\s(?<description>.*)$";
        string pattern41 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-]R\d+\s(?<description>.*)$";
        string pattern42 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-]REV\d+\s(?<description>.*)$";
        string pattern43 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-]Rev\d+\s(?<description>.*)$";
        string pattern44 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-][Rr]ev[_ ]\d+\s(?<description>.*)$";
        string pattern45 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-][Rr]\d+\s(?<description>.*)$";
        string pattern46 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-][Rr]\d+[_ ]?\s(?<description>.*)$";
        string pattern47 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[_-][A-Z][_]\d{4})[_-][Rr][eE][vV]\s?\d+\s(?<description>.*)$";
        string pattern48 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][_]\d{3})[_-][Rr][eE][vV]\s?\d+\s(?<description>.*)$";
        string pattern49 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][_]\d{2})[_-][Rr][eE][vV]\s?\d+\s(?<description>.*)$";
        string pattern50 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][_]\d{3}[_-][A-Z])[_-][Rr][eE][vV]\s?\d+\s(?<description>.*)$";
        string pattern51 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}-[A-Z]-\d{4})_(?<description>.+)_REV\s?(?<revision>\d+)_V(?<version>\d+)\sPrint$";
        string pattern52 = @"^(?<sheetName>JAC\sTEST)$";
        string pattern53 = @"^(?<sheetName>\d{4}\s-\s\d{4}-[A-Z]-\d{4})-REV\s\d+\s?-\s?(?<description>.*)$";
        string pattern54 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}[A-Z]-R-\d{4})\s-\s\((?<description>.*?)\)\s-\sREV\s?\d+$";
        string pattern55 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}[A-Z]-R-\d{4})\s-\s\((?<description>.*?)\)\s-\sREV\s?\d+$";
        string pattern56 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}[A-Z]-R-\d{4})\s-\s\((?<description>.*?)\)\s-\sREV\s?\d+$";
        string pattern57 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z]-R-\d{4})[\s-]\((?<description>.*?)\)[\s-][Rr][\s-]?\d+$";
        string pattern58 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z][\s-][A-Z]-\d{4})[\s-]\((?<description>.*?)\)[\s-]REV[\s-]?\d+$";
        string pattern59 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z][\s-][A-Z]-\d{4})[\s-]\((?<description>.*?)\)[\s-][Rr][Ee][Vv][\s-]?\d+$";
        string pattern60 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z][\s-][A-Z]-\d{4})[\s-]\((?<description>.*?)\)[\s-][Rr][\s-]?\d+$";
        string pattern61 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][\s-]\d{3})[\s-]\((?<description>.*?)\)[\s-][Rr][Ee][Vv][\s-]?\d+$";
        string pattern62 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z][\s-]\d{2})[\s-]\((?<description>.*?)\)[\s-][Rr][Ee][Vv][\s-]?\d+$";
        string pattern63 = @"^(?<sheetName>\d{4}_\d{4}-\d{2}[A-Z]-[A-Z]-\d{4})\s-\s\((?<description>.*?)\)\s-\sREV\s?\d+$";
        string pattern64 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+)$";
        string pattern65 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+?)(?:\s+-\s+REV\s?\d+)?$";
        string pattern66 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+\((?<description>.+?)\)\s+-\s+REV\s?\d+$";
        string pattern67 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+?)\s+-\s+REV\s?\d+$";
        string pattern68 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-][A-Z]-\d{4})$";
        string pattern69 = @"^(?<sheetName>\d{4}[_-]\d{4}[_][A-Z]_\d{4})$";
        string pattern70 = @"^(?<sheetName>\d{4}[_-]\d{4}[_][A-Z][_]\d{4})\s+(?<revision>REV\d{2})\s+(?<description>.*)$";
        string pattern71 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{3})\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\d{2})$";
        string pattern72 = @"^(?<sheetName>\d{4}[_]\d{4}[-]\d{2}[-][A-Z][-]\d{4})\s+\((?<description>.+)\)$";
        string pattern73 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[-]\d{4})\s+\((?<description>.+)\)$";
        string pattern74 = @"^(?<sheetName>\d{4}[_]\d{4}[-][A-Z][-]\d{2}[A-Z]{2}[-]\d{4})\s+-\s+(?<description>.+)$";
        string pattern75 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+?)\s+-\s+(?<revision>REV\s?\d+)?$";
        string pattern76 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+\((?<description>.+?)\)\s+-\s+(?<revision>REV\s?\d+)?$";
        string pattern77 = @"^(?<sheetName>(?:[^\s-]+-){3,4}[^\s-]+)\s+-\s+(?<description>.+?)\s+-\s+REV\s?\d+$";
        string pattern78 = @"^(?<sheetName>\d{4}[_-]\d{4}[_-]\d{2}[A-Z][_]\d{4})[_-]REV?\d+\s?(?<description>.*)?$";
        string pattern79 = @"^(?<sheetName>\d{4}[_-]\d{4}[-]\d{2}[-][A-Z]-\d{4}\.\d+)\((?<description>.*)\)$";

        var regexPatterns = new[]
        {
        pattern, pattern2, pattern3, pattern4, pattern5, pattern6, pattern7, pattern8, pattern9, pattern10,
        pattern11, pattern12, pattern13, pattern14, pattern15, pattern16, pattern17, pattern18, pattern19, pattern20,
        pattern21, pattern22, pattern23, pattern24, pattern25, pattern26, pattern27, pattern28, pattern29, pattern30,
        pattern31, pattern32, pattern33, pattern34, pattern35, pattern36, pattern37, pattern38, pattern39, pattern40,
        pattern41, pattern42, pattern43, pattern44, pattern45, pattern46, pattern47, pattern48, pattern49, pattern50,
        pattern51, pattern52, pattern53, pattern54, pattern55, pattern56, pattern57, pattern58, pattern59, pattern60,
        pattern61, pattern62, pattern63, pattern64, pattern65, pattern66, pattern67, pattern68, pattern69, pattern70,
        pattern71, pattern72, pattern73, pattern74, pattern75, pattern76, pattern77, pattern78, pattern79
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            };

        foreach (var patterner in regexPatterns)
        {
            var match = Regex.Match(fileName, patterner);
            if (match.Success)
            {
                sheetName = match.Groups["sheetName"].Value;
                description = match.Groups["description"].Success ? match.Groups["description"].Value : string.Empty;
                break;
            }
        }

        if (string.IsNullOrEmpty(sheetName))
        {
            string[] parts = fileName.Split('-');

            // If the parts are bigger than three
            if (parts.Length > 4)
            {
                sheetName = string.Join("-", parts.Take(4)).Trim();
                description = string.Join("-", parts.Skip(4)).Trim();
            }
            else if (parts.Length > 3 && parts[3].Contains("("))
            {
                sheetName = string.Join("-", parts.Take(3)).Trim();
                description = string.Join("-", parts.Skip(3)).Trim();
            }
            else
            {
                sheetName = fileName;
                description = string.Empty;
            }
        }

        // Clean up the description by removing unnecessary parts
        // Handle parentheses in description
        int parenthesesIndex = description.IndexOf('(');
        if (parenthesesIndex != -1)
        {
            description = description.Substring(parenthesesIndex + 1);
            int endParenthesesIndex = description.IndexOf(')');
            if (endParenthesesIndex != -1)
            {
                description = description.Substring(0, endParenthesesIndex).Trim();
            }
        }

        // Remove trailing "REV" information from the description if it exists
        const string revSeparator = "REV ";
        int revIndex = description.LastIndexOf(revSeparator, StringComparison.OrdinalIgnoreCase);
        if (revIndex != -1)
        {
            description = description.Substring(0, revIndex).Trim();
        }

        // Remove leading/trailing underscores, dashes, or spaces
        description = description.Trim('-', '_', ' ');

        return new SheetDetails
        {
            SheetName = RemoveRev(sheetName.Trim().Trim(' ').Replace(" ", "").Trim('.').Trim('-')),
            Description = RemoveRev(description.Trim().Trim(' ').Trim('.').Trim('-'))
        };
    };


    Func<string, string, SheetDetails> extractSheetNameAndDescriptionRevisionAndVersion = (fileName, projectName) =>
    {
        string sheetName = string.Empty;
        string description = string.Empty;
        string projectNumber = string.Empty;
        string revision = string.Empty;
        string version = string.Empty;

        // Regular expression pattern to match the required data
        string pattern = @"^(?<project_number>\d{4}[-_]\d{4})[-_](?<sheet_name>[A-Za-z0-9\-_.]+-?)\s?(?<description>.*?)(?:\s+(?<revision>REV\s?\d{1,2}|REV\d{2}|rev\s?\d{1,2}|rev\d{2}|Rev\s?\d{1,2}|Rev\d{2}|r\s?\d{1,2}|r\d{2}|R\d{1,2}))?(?:_v(?<version>\d+))?\.pdf$";
        string pattern2 = @"^(?<project_number>\d{4}[-_]\d{4})[-_](?<sheet_name>[A-Za-z0-9\-_.]+-?)\s?(?<description>.*?)(?:\s+(?<revision>(?i:rev|r)\s?\d{1,2}))?(?:_v(?<version>\d+))?\.pdf$";
        string pattern3 = @"^(?<project_number>\d{2,4}[-_]\d{3,4}[-_A-Za-z0-9]*)[-_\s]?(?<sheet_name>[A-Za-z0-9\-_.\s]+?)[\s_-]+(?<description>.*?)(?:[\s_-]+(?<revision>(?i:rev|r|REV)\s?\d{1,2}))?(?:_v(?<version>\d+))?\s?(?:\((?<additional_info>.*)\))?\.pdf$";
        string pattern4 = @"^(?<project_number>\d{3}-P)\s+(?<revision>REV\s\d+)\s+-\s+(?<description>.+)\.pdf$";
        string pattern5 = @"^(?<project_number>\d{2,4}-\d{2,4})\s*-\s*(?<sheet_name>[A-Z0-9]+(?:-[A-Z0-9]+)*)\s*-\s*REV\s*(?<revision>\d+)\s*-\s*\(\s*(?<description>.+?)\s*\)\s*-\s*(?<extra_info>\d+)\.pdf$";
        string pattern6 = @"^(?<project_number>\d{2}-\d{3})\s*-\s*(?<sheet_name>[A-Z0-9]+(?:-\d+)?)\s*(?:REV\s*(?<revision>\d+))?\s*-\s*(?<description>.+?)\.pdf$";


        string projectNumberPattern = @"^(?<project_number>\d{4}[-_]\d{4})";
        string sheetNamePattern = @"(?<sheet_name>[A-Za-z0-9\-_.]+-?)";
        string revisionPattern = @"(REV\s?\d{1,2}|REV\d{2}|rev\s?\d{1,2}|rev\d{2}|Rev\s?\d{1,2}|Rev\d{2}|r\s?\d{1,2}|r\d{2}|R\d{1,2})";
        string versionPattern = @"(_v(?<version>\d+))?\.pdf$";
        string revisionpattern2 = @"(?i)\b(rev\s?0|rev\s?1|rev\s?2|rev\s?3|rev\s?4|rev\s?5|rev\s?6|rev\s?7|rev\s?8|rev\s?9|rev\s?10|rev\s?11|rev00|rev01|rev02|rev03|rev04|rev05|rev06|rev07|rev08|rev09|rev10|rev11|rev0|rev1|rev2|rev3|rev4|rev5|rev6|rev7|rev8|rev9|rev10|rev11|r\s?0|r\s?1|r\s?2|r\s?3|r\s?4|r\s?5|r\s?6|r\s?7|r\s?8|r\s?9|r\s?10|r\s?11|r00|r01|r02|r03|r04|r05|r06|r07|r08|r09|r10|r11|r0|r1|r2|r3|r4|r5|r6|r7|r8|r9|r10|r11)\b";
        string revisionpattern3 = @"(?i)\b(rev\s?0|rev\s?1|rev\s?2|rev\s?3|rev\s?4|rev\s?5|rev\s?6|rev\s?7|rev\s?8|rev\s?9|rev\s?10|rev\s?11|rev00|rev01|rev02|rev03|rev04|rev05|rev06|rev07|rev08|rev09|rev10|rev11|rev0|rev1|rev2|rev3|rev4|rev5|rev6|rev7|rev8|rev9|rev10|rev11|r\s?0|r\s?1|r\s?2|r\s?3|r\s?4|r\s?5|r\s?6|r\s?7|r\s?8|r\s?9|r\s?10|r\s?11|r00|r01|r02|r03|r04|r05|r06|r07|r08|r09|r10|r11|r0|r1|r2|r3|r4|r5|r6|r7|r8|r9|r10|r11|[a-z]|[a-z][a-z])\b";
        string revisionpattern4 = @"(?i)\b(rev\s?[0-9]+|rev\s?[a-z]|r\s?[0-9]+|r\s?[a-z])\b";

        // Combine patterns to match different parts of the filename
        string combinedPattern = $"{projectNumberPattern}[-_]{sheetNamePattern}.*";

        string revisionValue = "";
        // Extract data from filenames
        Match match1 = Regex.Match(fileName, revisionpattern2);
        if (match1.Success)
        {
            revision = match1.Success ? match1.Value : "N/A";

            //revision = match1.Groups["revision"].Success ? match1.Groups["revision"].Value : "N/A";
            revisionValue = revision;
        }

        Match match = Regex.Match(fileName, pattern2);
        Match Nextmatch1 = Regex.Match(fileName, pattern4);
        Match Nextmatch2 = Regex.Match(fileName, pattern5);

        Match matcher1 = Regex.Match(fileName, pattern6);

        if (match.Success)
        {

            //Get the revision number first
            //if its null make it zero

            //revision = match.Groups["revision"].Success ? match.Groups["revision"].Value : "N/A";

            description = match.Groups["description"].Value;
            sheetName = match.Groups["sheet_name"].Value;

            if (revision != "N/A" && revision != "")
            {
                description = description.Replace(revisionValue, "").Trim();
                sheetName = sheetName.Replace(revisionValue, "").Trim();
            }

            string extraPattern = @"([A-Za-z]+)$";
            Match extraMatch = Regex.Match(sheetName, extraPattern);
            if (extraMatch.Success)
            {
                string extra = extraMatch.Value;
                description = $"{extra} {description}".Trim();
                sheetName = sheetName.Replace(extra, "").TrimEnd('-');

                // Remove the extraneous part and any trailing hyphen

            }

            //If the revision went inside the revision
            if (revision == "N/A" || revision == "")
            {
                Match match3 = Regex.Match(description, revisionpattern4);
                if (match3.Success)
                {
                    revision = match3.Success ? match3.Value : "N/A";

                    //revision = match1.Groups["revision"].Success ? match1.Groups["revision"].Value : "N/A";
                    revisionValue = revision;
                    description = description.Replace(revisionValue, "").Trim();
                }
            }

            // projectNumber = match.Groups["project_number"].Value;
            // sheetName = match.Groups["sheet_name"].Value;

            //revision = match.Groups["revision"].Success ? match.Groups["revision"].Value : "N/A";
            //description = match.Groups["description"].Value;
            //version = match.Groups["version"].Value;
        }
        //else if (Nextmatch1.Success)
        //{
        //    revision = Nextmatch1.Groups["revision"].Success ? Nextmatch1.Groups["revision"].Value : "N/A";
        //    description = Nextmatch1.Groups["description"].Value;
        //    sheetName = Nextmatch1.Groups["sheet_name"].Value;

        //    if (revision != "N/A" && revision != "")
        //    {
        //        description = description.Replace(revision, "").Trim();
        //        sheetName = sheetName.Replace(revision, "").Trim();
        //    }

        //    string extraPattern = @"([A-Za-z]+)$";
        //    Match extraMatch = Regex.Match(sheetName, extraPattern);
        //    if (extraMatch.Success)
        //    {
        //        string extra = extraMatch.Value;
        //        description = $"{extra} {description}".Trim();
        //        sheetName = sheetName.Replace(extra, "").TrimEnd('-');
        //    }

        //    // If the revision went inside the description
        //    if (revision == "N/A" || revision == "")
        //    {
        //        Match matchRevision = Regex.Match(description, @"(?i:rev|r)\s?\d{1,2}");
        //        if (matchRevision.Success)
        //        {
        //            revision = matchRevision.Value;
        //            description = description.Replace(revision, "").Trim();
        //        }
        //    }

        //    projectNumber = match.Groups["project_number"].Value;
        //}
        else if (Nextmatch2.Success)
        {
            revision = Nextmatch2.Groups["revision"].Success ? Nextmatch2.Groups["revision"].Value : "N/A";
            description = Nextmatch2.Groups["description"].Value;
            sheetName = Nextmatch2.Groups["sheet_name"].Value;

            if (revision != "N/A" && revision != "")
            {
                description = description.Replace(revision, "").Trim();
                sheetName = sheetName.Replace(revision, "").Trim();
            }

            string extraPattern = @"([A-Za-z]+)$";
            Match extraMatch = Regex.Match(sheetName, extraPattern);
            if (extraMatch.Success)
            {
                string extra = extraMatch.Value;
                description = $"{extra} {description}".Trim();
                sheetName = sheetName.Replace(extra, "").TrimEnd('-');
            }

            // If the revision went inside the description
            if (revision == "N/A" || revision == "")
            {
                Match matchRevision = Regex.Match(description, @"(?i:rev|r)\s?\d{1,2}");
                if (matchRevision.Success)
                {
                    revision = matchRevision.Value;
                    description = description.Replace(revision, "").Trim();
                }
            }

            projectNumber = match.Groups["project_number"].Value;
        }



        else if (matcher1.Success)
        {
            //separate the rev and sheet name from the description

            projectNumber = matcher1.Groups["project_number"].Value;
            sheetName = matcher1.Groups["sheet_name"].Value;
            revision = matcher1.Groups["revision"].Success ? matcher1.Groups["revision"].Value : "N/A";
            description = matcher1.Groups["description"].Value;

            if (revision != "N/A" && revision != "")
            {
                description = description.Replace(revision, "").Trim();
                sheetName = sheetName.Replace(revision, "").Trim();
            }

            string extraPattern = @"([A-Za-z]+)$";
            Match extraMatch = Regex.Match(sheetName, extraPattern);
            if (extraMatch.Success)
            {
                string extra = extraMatch.Value;
                description = $"{extra} {description}".Trim();
                sheetName = sheetName.Replace(extra, "").TrimEnd('-');
            }

            // If the revision went inside the description
            if (revision == "N/A" || revision == "")
            {
                Match matchRevision = Regex.Match(description, @"(?i:rev|r)\s?\d{1,2}");
                if (matchRevision.Success)
                {
                    revision = matchRevision.Value;
                    description = description.Replace(revision, "").Trim();
                }
            }
        }
        else
        {
            //if (fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
            //{
            //    fileName = fileName.Substring(0, fileName.Length - 4).Trim();
            //}

            //string pattern12 = @"^(?<project_number>\d{2,4}-[A-Z0-9]+)\s*(?:REV\s*(?<revision>\d+)\s*-\s*)?(?<sheet_name>.*?)\s*-\s*(?<description>.+?)\.pdf$";

            //string pattern12 = @"^(?<sheet_name>\d{2,4}-[A-Z0-9]+)\s*(?:REV\s*(?<revision>\d+)\s*-\s*)?(?<description>.+?)(?:\s*\(Rev\s*(?<extra_revision>\d+)\))?\.pdf$";

            // string pattern12 = @"^(?<sheet_name>\d{2,4}[-_\s]?[A-Z0-9]+(?:[-_\s]?[A-Z0-9]+)*)\s*(?:-\s*REV\s*(?<revision>\d+))?\s*-\s*(?<description>.+?)(?:\s*\(Rev\s*(?<extra_revision>\d+)\))?\.pdf$";

            //string pattern12 = @"^(?<sheetName>(\d{4}[-_]\d{4}[-_][A-Z]-\d{4})|(\d{3}-[A-Z])\sREV\s\d+\s-\s|(\d{4}-\d{3}-\sBS\d{4})\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)-\d+|(\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+?)\s+\(Rev\s+\d+\)|(\d{2}-\d{3}\s+-\s+BS\d{4}-\d)\s+-\s+(?<description>.+)|(\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+)|(\d{2}-\d{3}\s+-\s+BS\d{4}\.\d)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\))\.pdf$";



            string pattern12 = @"^(?<sheetName>(\d{4}[-_]\d{4}[-_][A-Z]-\d{4})|(\d{3}-[A-Z])\sREV\s\d+\s-\s|(\d{4}-\d{3}-\sBS\d{4})\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\)-\d+|(\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+?)\s+\(Rev\s+\d+\)|(\d{2}-\d{3}\s+-\s+BS\d{4}-\d)\s+-\s+(?<description>.+)|(\d{2}-\d{3}\s+-\s+BS\d{4})\s+-\s+(?<description>.+)|(\d{2}-\d{3}\s+-\s+BS\d{4}\.\d)\s+-\s+REV\s+\d+\s+-\s+\(\s*(?<description>.+?)\s*\))\.pdf$";

            Match matcher2 = Regex.Match(fileName, pattern12);

            if (matcher2.Success)
            {
                //separate the rev and sheet name from the description

                projectNumber = matcher1.Groups["project_number"].Value;
                sheetName = matcher1.Groups["sheet_name"].Value;
                revision = matcher1.Groups["revision"].Success ? matcher1.Groups["revision"].Value : "N/A";
                description = matcher1.Groups["description"].Value;

                if (revision != "N/A" && revision != "")
                {
                    description = description.Replace(revision, "").Trim();
                    sheetName = sheetName.Replace(revision, "").Trim();
                }

                string extraPattern = @"([A-Za-z]+)$";
                Match extraMatch = Regex.Match(sheetName, extraPattern);
                if (extraMatch.Success)
                {
                    string extra = extraMatch.Value;
                    description = $"{extra} {description}".Trim();
                    sheetName = sheetName.Replace(extra, "").TrimEnd('-');
                }

                // If the revision went inside the description
                if (revision == "N/A" || revision == "")
                {
                    Match matchRevision = Regex.Match(description, @"(?i:rev|r)\s?\d{1,2}");
                    if (matchRevision.Success)
                    {
                        revision = matchRevision.Value;
                        description = description.Replace(revision, "").Trim();
                    }
                }
            }
        }

        if (String.IsNullOrEmpty(projectNumber))
        {
            var projectNumnersplit = projectName.Split('-');
            projectNumber = projectNumnersplit[0].Replace(" ", "");
        }

        string cleanDescription = description;

        string Description = cleanDescription
    .Replace(" ( ", " ")
    .Replace(" ) ", " ")
    .Replace(" - ", " ")
    .Replace("(", "")
    .Replace(")", "")
    .Replace("-", "");

        if (sheetName == "C-4000")

            if (!string.IsNullOrEmpty(sheetName))
            {

                int lastHyphenIndex = sheetName.LastIndexOf('-');

                if (lastHyphenIndex == sheetName.Length - 1)
                {
                    sheetName = sheetName.Remove(lastHyphenIndex, 1);
                }
            }

        return new SheetDetails
        {
            SheetName = projectNumber + "-" + sheetName,
            Description = Description,
            Revision = revision
        };
    };


    Func<string, SheetDetails> extractSheetDetails = (fileName) =>
    {
        // Split the string by commas and trim the parts
        string[] parts = fileName.Split(',');

        // Create and return the SheetDetails object
        return new SheetDetails
        {
            SheetName = parts.Length > 0 ? parts[0].Trim() : string.Empty,
            Description = parts.Length > 1 ? parts[1].Trim() : string.Empty,
            Revision = parts.Length > 2 ? parts[2].Trim() : string.Empty,
            SheetSize = parts.Length > 3 ? parts[3].Trim() : string.Empty
        };
    };

    string RemoveRevision(string revisionPattern, string filename, string revision)
    {
        if (revision == "N/A")
        {
            Match match = Regex.Match(filename, revisionPattern);
            if (match.Success)
            {
                revision = match.Groups["revision"].Success ? match.Groups["revision"].Value : "N/A";
            }
        }
        return revision;
    }

    #region Old MEthod
    //var uniqueSheets = new Dictionary<string, SheetDetails>();
    //var revisionPattern2 = @"(?i:rev|r)\s?(\d{1,2})";

    //foreach (var pdf in Model)
    //{
    //    var details = extractSheetDetails(pdf);

    //    //var details = extractSheetNameAndDescriptionRegular(pdf.attributes.displayName.ToString());

    //    // Extract and format the revision number
    //    //var match = Regex.Match(details.Revision, revisionPattern2, RegexOptions.IgnoreCase);
    //    //if (match.Success)
    //    //{
    //    //    details.Revision = int.Parse(match.Groups[1].Value).ToString(); // Convert "00" to "0", "01" to "1", etc.
    //    //}


    //    //var uniqueKey = details.SheetName + details.Description.TrimStart().TrimEnd();

    //    //var uniqueKey = details.SheetName + details.Description;

    //    //if (!uniqueSheets.ContainsKey(uniqueKey))
    //    //{
    //    //    uniqueSheets[uniqueKey] = details;
    //    //}
    //    //else if (string.Compare(uniqueSheets[uniqueKey].Revision, details.Revision, StringComparison.Ordinal) < 0)
    //    //{
    //    //    uniqueSheets[uniqueKey] = details;
    //    //}
    //}

    //var uniqueSheets = new Dictionary<string, SheetDetails>();
    //var revisionPattern2 = @"(?:\s+(?<revision>(?i:rev|r)\s?\d{1,2}))";

    //foreach (var pdf in Model)
    //{
    //    var details = extractSheetNameAndDescriptionRevisionAndVersion(pdf.attributes.displayName.ToString(), ViewBag.ProjectName);
    //    //var details = extractSheetNameAndDescriptionRegular(pdf.attributes.displayName.ToString());

    //    // Remove the revision prefix from the details.Revision
    //    details.Revision = Regex.Replace(details.Revision, revisionPattern2, string.Empty, RegexOptions.IgnoreCase).Trim();

    //    var uniqueKey = details.SheetName + details.Description.TrimStart().TrimEnd();

    //    if (!uniqueSheets.ContainsKey(uniqueKey))
    //    {
    //        uniqueSheets[uniqueKey] = details;
    //    }
    //    else if (string.Compare(uniqueSheets[uniqueKey].Revision, details.Revision, StringComparison.Ordinal) < 0)
    //    {
    //        uniqueSheets[uniqueKey] = details;
    //    }
    //}

    //var uniqueSheets = new Dictionary<string, SheetDetails>();

    //foreach (var pdf in Model)
    //{
    //    var details = extractSheetNameAndDescriptionRevisionAndVersion(pdf.attributes.displayName.ToString(), ViewBag.ProjectName);
    //    //var details = extractSheetNameAndDescriptionRegular(pdf.attributes.displayName.ToString());

    //    var uniqueKey = details.SheetName + details.Description.TrimStart().TrimEnd() + details.Revision;

    //    if (!uniqueSheets.ContainsKey(uniqueKey))
    //    {
    //        uniqueSheets[uniqueKey] = details;
    //    }
    //}
    #endregion

    string RemoveRev(string details)
    {
        //R1 //Rev1 //rev1
        return details.Replace("REV 0", "").Replace("REV 1", "").
            Replace("REV 2", "").Replace("REV 3", "")
            .Replace("REV 4", "").Replace("REV 5", "")
            .Replace("REV 6", "").Replace("REV 7", "")
            .Replace("REV 8", "").Replace("REV 9", "")
            .Replace("REV 10", "").Replace("REV 11", "")
            .Replace("REV00", "").Replace("REV01", "").
            Replace("REV02", "").Replace("REV03", "")
            .Replace("REV04", "").Replace("REV05", "")
            .Replace("REV06", "").Replace("REV07", "")
            .Replace("REV08", "").Replace("REV09", "")
            .Replace("REV10", "").Replace("REV11", "")
            .Replace("REV0", "").Replace("REV1", "").
            Replace("REV2", "").Replace("REV3", "")
            .Replace("REV4", "").Replace("REV5", "")
            .Replace("REV6", "").Replace("REV7", "")
            .Replace("REV8", "").Replace("REV9", "")
            .Replace("REV10", "").Replace("REV11", "")

            .Replace("rev 0", "").Replace("rev 1", "").
            Replace("rev 2", "").Replace("rev 3", "")
            .Replace("rev 4", "").Replace("rev 5", "")
            .Replace("rev 6", "").Replace("rev 7", "")
            .Replace("rev 8", "").Replace("rev 9", "")
            .Replace("rev 10", "").Replace("rev 11", "")
            .Replace("rev00", "").Replace("rev01", "").
            Replace("rev02", "").Replace("rev03", "")
            .Replace("rev04", "").Replace("rev05", "")
            .Replace("rev06", "").Replace("rev07", "")
            .Replace("rev08", "").Replace("rev09", "")
            .Replace("rev10", "").Replace("rev11", "")
            .Replace("rev0", "").Replace("rev1", "").
            Replace("rev2", "").Replace("rev3", "")
            .Replace("rev4", "").Replace("rev5", "")
            .Replace("rev6", "").Replace("rev7", "")
            .Replace("rev8", "").Replace("rev9", "")
            .Replace("rev10", "").Replace("rev11", "")

            .Replace("Rev 0", "").Replace("Rev 1", "").
            Replace("Rev 2", "").Replace("Rev 3", "")
            .Replace("Rev 4", "").Replace("Rev 5", "")
            .Replace("Rev 6", "").Replace("Rev 7", "")
            .Replace("Rev 8", "").Replace("Rev 9", "")
            .Replace("Rev 10", "").Replace("Rev 11", "")
            .Replace("Rev00", "").Replace("Rev01", "").
            Replace("Rev02", "").Replace("Rev03", "")
            .Replace("Rev04", "").Replace("Rev05", "")
            .Replace("Rev06", "").Replace("Rev07", "")
            .Replace("Rev08", "").Replace("Rev09", "")
            .Replace("Rev10", "").Replace("Rev11", "")
            .Replace("Rev0", "").Replace("Rev1", "").
            Replace("Rev2", "").Replace("Rev3", "")
            .Replace("Rev4", "").Replace("Rev5", "")
            .Replace("Rev6", "").Replace("Rev7", "")
            .Replace("Rev8", "").Replace("Rev9", "")
            .Replace("Rev10", "").Replace("Rev11", "")

            .Replace("r 0", "").Replace("r 1", "").
            Replace("r 2", "").Replace("r 3", "")
            .Replace("r 4", "").Replace("r 5", "")
            .Replace("r 6", "").Replace("r 7", "")
            .Replace("r 8", "").Replace("r 9", "")
            .Replace("r 10", "").Replace("r 11", "")
            .Replace("r00", "").Replace("r", "").
            Replace("r02", "").Replace("r", "")
            .Replace("r04", "").Replace("r05", "")
            .Replace("r06", "").Replace("r07", "")
            .Replace("r08", "").Replace("r09", "")
            .Replace("r10", "").Replace("r11", "")
            .Replace("R0", "").Replace("R1", "").
            Replace("R2", "").Replace("R3", "")
            .Replace("R4", "").Replace("R5", "")
            .Replace("R6", "").Replace("R7", "")
            .Replace("R8", "").Replace("R9", "")
            .Replace("R10", "").Replace("R11", "");
    }
}

<div class="pagetitle">
    <h1>Project</h1>
    <nav>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
            <li class="breadcrumb-item active">Select Sheet</li>
        </ol>
    </nav>
</div>
<!-- End Page Title -->
<section class="section">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Project: @ViewBag.ProjectName</h5>
                    <form method="post" action="@Url.Action("ShowSelectedPdfs", "Hubs")">

                        <input type="hidden" name="hubId" value="@ViewBag.hubId" />
                        <input type="hidden" name="projectId" value="@ViewBag.projectId" />
                        <input type="hidden" name="tokens" value="@ViewBag.tokens" />
                        <input type="hidden" name="projectName" value="@ViewBag.ProjectName" />

                        <button type="submit" class="btn btn-primary">Create Transmittal</button>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Select</th>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Revision</th>
                                    <th>Sheet Size</th>
                                    <th style="color:white" data-type="date" data-format="YYYY/DD/MM"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var pdf in Model)
                                {
                                    var details = extractSheetDetails(pdf);

                                    <tr>
                                        <td>
                                            <input type="checkbox" name="selectedPdfs" value="@details.SheetName,@details.Description,@details.Revision,@details.SheetSize" />
                                        </td>
                                        <td>@details.SheetName</td>
                                        <td>@details.Description</td>
                                        <td>@details.Revision</td>
                                        <td>@details.SheetSize</td>
                                        <td style="color:white"></td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Fix JavaScript errors and ensure form submission works
document.addEventListener('DOMContentLoaded', function() {
    // Catch and suppress DataTables errors
    window.addEventListener('error', function(e) {
        if (e.message && (e.message.includes('DataTable') || e.message.includes('appendChild'))) {
            e.preventDefault();
            console.log('DataTables error suppressed:', e.message);
            return false;
        }
    });

    // Ensure form submission works even if there are JavaScript errors
    const form = document.querySelector('form[action*="ShowSelectedPdfs"]');
    const submitButton = document.querySelector('button[type="submit"]');

    if (form && submitButton) {
        submitButton.addEventListener('click', function(e) {
            // Force form submission even if other scripts fail
            setTimeout(function() {
                if (!form.submitted) {
                    form.submitted = true;
                    form.submit();
                }
            }, 100);
        });
    }
});
</script>
