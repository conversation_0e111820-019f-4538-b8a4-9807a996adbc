-- Create StruxitTwo Database and Basic Tables
-- Run this script in SQL Server Management Studio or Visual Studio

USE master;
GO

-- Create the database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'StruxitTwo')
BEGIN
    CREATE DATABASE StruxitTwo;
END
GO

USE StruxitTwo;
GO

-- Create Company table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Company' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Company] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Name] NVARCHAR(255) NOT NULL
    );
END
GO

-- Create Sheets table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Sheets' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Sheets] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Name] NVARCHAR(255) NOT NULL,
        [Description] NVARCHAR(MAX) NULL,
        [SheetSize] NVARCHAR(50) NULL
    );
END
GO

-- Create MissingTransmittalData table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MissingTransmittalData' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MissingTransmittalData] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [Sheet] NVARCHAR(255) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [Rev] NVARCHAR(50) NULL,
        [Project Name] NVARCHAR(255) NULL,
        [Company] NVARCHAR(255) NULL,
        [Date] NVARCHAR(50) NULL
    );
END
GO

-- Create user table (if needed)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[user] (
        [ID] INT IDENTITY(1,1) PRIMARY KEY,
        [Email] NVARCHAR(255) NULL,
        [password] NVARCHAR(255) NULL,
        [Name] NVARCHAR(255) NULL,
        [Hint] NVARCHAR(255) NULL,
        [Website] NVARCHAR(255) NULL,
        [ArtistName] NVARCHAR(255) NULL,
        [SmallText] NVARCHAR(MAX) NULL,
        [desc] NVARCHAR(MAX) NULL,
        [EditID] INT NULL,
        [ProfileID] INT NULL,
        [Mixtape] NVARCHAR(255) NULL,
        [RecordLabel] NVARCHAR(255) NULL
    );
END
GO

-- Create Transmittals table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Transmittals' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Transmittals] (
        [ID] INT IDENTITY(1,1) PRIMARY KEY,
        [TransNo] NVARCHAR(255) NULL,
        [No] NVARCHAR(255) NULL,
        [isDeleted] BIT NOT NULL DEFAULT 0,
        [DateDeleted] DATETIME NULL,
        [Date] DATETIME NULL,
        [CompanyID] NVARCHAR(255) NULL,
        [PersonID] NVARCHAR(255) NULL,
        [ProjectName] NVARCHAR(255) NULL,
        [ProjectID] NVARCHAR(255) NULL
    );
END
GO

-- Create Person table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Person' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Person] (
        [id] INT IDENTITY(1,1) PRIMARY KEY,
        [Name] NVARCHAR(255) NULL
    );
END
GO

-- Insert some sample data
INSERT INTO [dbo].[Company] ([Name]) VALUES ('Modena AEC');
INSERT INTO [dbo].[Company] ([Name]) VALUES ('Test Company');

INSERT INTO [dbo].[Person] ([Name]) VALUES ('John Doe');
INSERT INTO [dbo].[Person] ([Name]) VALUES ('Jane Smith');

PRINT 'Database StruxitTwo created successfully with all required tables!';
