﻿using Struxit2._0.Services;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Mvc;
using Struxit2._0.Model;
using Struxit2._0.Services;
using Autodesk.Forge.Model;
using System;
using Struxit2._0.DataAccess;
using Struxit2._0.Interfaces;
using Struxit2._0.Helpers;
using System.Linq;
using static System.Net.WebRequestMethods;
using Antlr.Runtime;
using Tokens = Struxit2._0.Services.Tokens;
using Struxit2._0.Controllers;
using Autodesk.Forge;
using System.Data.SqlClient;
using System.IO;
using System.Net.Http.Headers;
using System.Net.Http;
using Amazon.Runtime.Internal.Transform;
using MongoDB.Driver.Core.Operations;
using static Struxit2._0.Services.AutodeskApiService;
using System.Security.Cryptography;
using System.Threading;
using Newtonsoft.Json.Linq;
using System.Runtime.InteropServices;

namespace Struxit2._0.Controllers
{
    public class HubsController : Controller
    {
        private readonly APS _apsService;

        private bool isServer = false;

        private string LocalClientID = "9wJGHEq5zMQnagsyuvhqDXtSD2kq8np2bBR9zAQOZd8LgP6I";
        private string LocalClientSecret = "XG1Dz8Y62lNhrDyQ6QRwFotAcZtDQbQYvpKZ9VAAsXlqgSJnFGhMpch0OG5z1I0X";
        private string LocalCallBackUrl = "https://localhost:44381/auth/callback";

        //Struxit server
        private string ServerClientID = "i2aHfz4at1MeAUjkX1k8vScjYNoZh2ysBPmwGVhUg1ZvVVAI";
        private string ServerClientSecret = "ROAxe53PaM7ux88WXWmpACMl3zTd9pRUVmlj6dzAgqroMq5MMf2tR7sZynmfYy3D";
        private string ServerCallBackUrl = "https://register.struxit.com/auth/callback";

        //Staging Server
        private string StagingClientID = "fmB418CkV3bEI1RZ4V23qMOlfnsENAdNsdjzENPNPXNJFC0c";
        private string StagingClientSecret = "X6MWlPB8YtKmKx8KpqosEpRMuvW0c6Zi2wrFTxhZ9iSSZOHAlmgKkMtzIoF4ku2d";
        private string StagingCallBackUrl = "http://register.struxit.com/auth/callback";

        private string clientID = "8rQfQ3yxpvRbHpjxreVEh3mB1XRXIwFLaH2VuGfOxZBtRnxj";   //"DJSITlZ89cJCUjQmDuJ8DpBVKXhJ7aU5WpqAWoW6JvAalQZs";                                                        //"uYTOK7cmoK8726nzLXcS0S2oQ32BfPJB1KzY6PRDtUrYWhuB";
        private string clientSecret = "VXvBMPV24S9xYw38gr4J6GMGtLoGIJzXNh1pu8MmYrgyXJTfeyLN1BGZ52VuA5Nk"; //"4fx4f0ezVX8nARZL0TlDY7qzhe3a0sro1Qk550keq5m6X3saWaL8vlfmCt8Pq6HH";                                                   //"2Jn75WxlsTU3nD4cdS8wrwXG7DzOpByGvNTiBwyTJTptnxOOUh2J8wgdqY6AuVeu";
        private string callbackURI = "https://struxit.reapertek.co.za/auth/callback";        //"https://localhost:44381/auth/callback"; //"https://struxit.reapertek.co.za/auth/callback"; //"https://localhost:44381/auth/callback"; //"https://struxit.reapertek.co.za/auth/callback";                   //"https://localhost:44381/auth/callback";

        private readonly ICompanyRepository _companyRepository;

        private readonly PdfService _pdfService;

        private MongoDBManager _mongoDBManager = new MongoDBManager();

        public HubsController(ICompanyRepository companyRepository, PdfService pdfService)
        {
            if (isServer)
            {
                clientID = ServerClientID;
                clientSecret = ServerClientSecret;
                callbackURI = ServerCallBackUrl;
            }
            else
            {
                clientID = LocalClientID;
                clientSecret = LocalClientSecret;
                callbackURI = LocalCallBackUrl;
            }

            _companyRepository = companyRepository;
            _apsService = new APS(clientID, clientSecret, callbackURI);
            _pdfService = pdfService;
        }

        public async Task<ActionResult> Index()
        {
            //return RedirectToAction("ZeldaPath", "Hubs");
            #region Original Index

            Tokens tokens = Session["tokens"] as Tokens;
            if (tokens == null)
            {
                return RedirectToAction("Login", "Auth");
            }

            if (tokens.ExpiresAt <= DateTime.UtcNow)
            {
                return RedirectToAction("RefreshToken", "Auth");
            }

            IEnumerable<dynamic> hubs = await _apsService.GetHubs(tokens);
            return View(hubs);

            #endregion
        }

        public async Task<ActionResult> Projects(string hubId)
        {
            Tokens tokens = Session["tokens"] as Tokens;
            if (tokens == null)
            {
                return RedirectToAction("Login", "Auth");
            }

            if (tokens.ExpiresAt <= DateTime.UtcNow)
            {
                return RedirectToAction("RefreshToken", "Auth");
            }

            IEnumerable<dynamic> projects = await _apsService.GetProjects(hubId, tokens);
            ViewBag.HubId = hubId;
            return View(projects);
        }

        public async Task<ActionResult> Contents(string hubId, string projectId, string folderId)
        {
            Tokens tokens = Session["tokens"] as Tokens;
            if (tokens == null)
            {
                return RedirectToAction("Login", "Auth");
            }

            if (tokens.ExpiresAt <= DateTime.UtcNow)
            {
                return RedirectToAction("RefreshToken", "Auth");
            }

            IEnumerable<dynamic> contents = await _apsService.GetContents(hubId, projectId, folderId, tokens);
            ViewBag.HubId = hubId;
            ViewBag.ProjectId = projectId;
            ViewBag.FolderId = folderId;
            return View(contents);
        }

        public async Task<ActionResult> Versions(string hubId, string projectId, string itemId)
        {
            Tokens tokens = Session["tokens"] as Tokens;
            if (tokens == null)
            {
                return RedirectToAction("Login", "Auth");
            }

            if (tokens.ExpiresAt <= DateTime.UtcNow)
            {
                return RedirectToAction("RefreshToken", "Auth");
            }

            IEnumerable<dynamic> versions = await _apsService.GetVersions(hubId, projectId, itemId, tokens);
            ViewBag.HubId = hubId;
            ViewBag.ProjectId = projectId;
            ViewBag.ItemId = itemId;
            return View(versions);
        }

        [HttpGet]
        public ActionResult SelectPage(string projectName, string hubId, string projectId)
        {
            var viewModel = new ProjectDetailViewModel
            {
                ProjectName = projectName,
                HubId = hubId,
                ProjectId = projectId
            };

            return View(viewModel);
        }

        [HttpPost]
        public ActionResult SelectTransmittal(string projectName, string hubId, string projectId)
        {
            return RedirectToAction("GetAllPdfs", "Hubs", new { hubId, projectId, projectName });
        }

        [HttpPost]
        public ActionResult SelectDrawingRegister(string projectName, string hubId, string projectId)
        {
            return RedirectToAction("DrawingRegister", "DrawingRegister", new { projectName, hubId, projectId });
        }

        public async Task<ActionResult> GetAllPdfs(string hubId, string projectId, string projectName)
        {

            #region Old Token Method

            //Tokens tokens = Session["tokens"] as Tokens;
            //if (tokens == null)
            //{
            //    return RedirectToAction("Login", "Auth");
            //}

            //var topFolders = await _apsService.GetContents(hubId, projectId, null, tokens);
            //var allPdfs = new List<dynamic>();

            //foreach (var folder in topFolders)
            //{
            //    await GetPdfsInFolder(hubId, projectId, folder["id"].ToString(), tokens, allPdfs);
            //}

            //return View(allPdfs);

            #endregion

            Tokens tokens = Session["tokens"] as Tokens;
            if (tokens == null)
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] GetAllPdfs ERROR - No tokens found in session, redirecting to Login");
                return RedirectToAction("Login", "Auth");
            }

            if (tokens.ExpiresAt <= DateTime.UtcNow)
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] GetAllPdfs ERROR - Tokens expired at {tokens.ExpiresAt}, redirecting to RefreshToken");
                return RedirectToAction("RefreshToken", "Auth");
            }

            var topFolders = await _apsService.GetContents(hubId, projectId, null, tokens);
            System.Diagnostics.Debug.WriteLine($"[DEBUG] Found {topFolders?.Count() ?? 0} top folders");

            var allPdfs = new List<dynamic>();
            var sheetNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            //Original Method

            #region Original Method

            foreach (var folder in topFolders)
            {
                //await GetPdfsInFolder(hubId, projectId, folder["id"].ToString(), tokens, allPdfs);

                if (!folder.attributes.ContainsKey("name"))
                {
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] Skipping folder without name attribute");
                    continue; // Skip if the 'name' attribute is not present
                }

                //string folderName = folder.attributes.name.ToString();

                string folderName = folder.attributes["name"].ToString();

                string folderId = folder.id.ToString();
                System.Diagnostics.Debug.WriteLine($"[DEBUG] Processing top folder: '{folderName}' (ID: {folderId})");

                // Let's see what's inside this folder first
                var folderContents = await _apsService.GetContents(hubId, projectId, folderId, tokens);
                System.Diagnostics.Debug.WriteLine($"[DEBUG] Folder '{folderName}' contains {folderContents?.Count() ?? 0} items");

                foreach (var item in folderContents)
                {
                    if (item.attributes.ContainsKey("name"))
                    {
                        string itemName = item.attributes["name"].ToString();
                        string itemType = item.type.ToString();
                        System.Diagnostics.Debug.WriteLine($"[DEBUG] - Item: '{itemName}' (Type: {itemType})");
                    }
                }

                // Look for the "plans" folder inside the current folder
                foreach (var item in folderContents)
                {
                    if (item.type.ToString() == "folders" &&
                        item.attributes.ContainsKey("name") &&
                        item.attributes["name"].ToString().Equals("plans", StringComparison.OrdinalIgnoreCase))
                    {
                        System.Diagnostics.Debug.WriteLine($"[DEBUG] Found 'plans' folder inside '{folderName}', getting contents...");
                        string plansFolderId = item.id.ToString();

                        // Get contents of the "plans" folder
                        var plansContents = await _apsService.GetContents(hubId, projectId, plansFolderId, tokens);
                        System.Diagnostics.Debug.WriteLine($"[DEBUG] Plans folder contains {plansContents?.Count() ?? 0} items");

                        // Iterate through the contents of the "plans" folder to find "CIVIL" and "STRUCTURAL"
                        foreach (var subFolder in plansContents)
                        {

                            if (!subFolder.attributes.ContainsKey("name"))
                            {
                                System.Diagnostics.Debug.WriteLine($"[DEBUG] Skipping subfolder without name attribute");
                                continue; // Skip if the 'name' attribute is not present
                            }

                            string subFolderName = subFolder.attributes["name"].ToString();

                            // string subFolderName = subFolder.attributes.name.ToString();

                            string subFolderId = subFolder.id.ToString();
                            System.Diagnostics.Debug.WriteLine($"[DEBUG] Found subfolder: '{subFolderName}' (ID: {subFolderId})");

                            if (subFolderName.IndexOf("CIVIL", StringComparison.OrdinalIgnoreCase) >= 0 || subFolderName.IndexOf("STRUCTURAL", StringComparison.OrdinalIgnoreCase) >= 0)
                            {
                                System.Diagnostics.Debug.WriteLine($"[DEBUG] Processing CIVIL/STRUCTURAL folder: '{subFolderName}'");
                                // Call the existing GetPdfsInFolder method for "CIVIL" and "STRUCTURAL" folders
                                // await GetPdfsInFolder(hubId, projectId, subFolderId, tokens, allPdfs);

                                List<string> pdfs = new List<string>();

                                await GetPdfsInFolder(hubId, projectId, subFolderId, tokens, pdfs);
                                System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder returned {pdfs.Count} PDFs for folder '{subFolderName}'");

                                //List<dynamic> pdfs = new List<dynamic>();

                                //await GetPdfsInFolder3(hubId, projectId, subFolderId, tokens, pdfs);

                                foreach (var pdf in pdfs)
                                {
                                    allPdfs.Add(pdf);
                                    System.Diagnostics.Debug.WriteLine($"[DEBUG] Added PDF to allPdfs collection");
                                }
                            }
                        }
                        break; // Found the plans folder, no need to continue
                    }
                }
            }

            #endregion

            ViewBag.ProjectName = projectName;
            ViewBag.hubId = hubId;
            ViewBag.projectId = projectId;
            ViewBag.tokens = tokens;

            System.Diagnostics.Debug.WriteLine($"[DEBUG] Final result: {allPdfs.Count} PDFs found total");
            return View(allPdfs);
        }

        private string GetBaseName(string pdfName)
        {
            // This method extracts the base name of the sheet, excluding the revision part.
            // It splits on the first occurrence of "REV" and trims the result.
            int revIndex = pdfName.IndexOf("REV", StringComparison.OrdinalIgnoreCase);
            if (revIndex >= 0)
            {
                return pdfName.Substring(0, revIndex).Trim();
            }
            return pdfName.Trim();
        }

        //Original
        private async Task GetPdfsInFolder(string hubId, string projectId, string folderId, Tokens tokens, List<string> pdfList)
        {
            //original
            var contents = await _apsService.GetContents(hubId, projectId, folderId, tokens);
            System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Found {contents?.Count() ?? 0} items in folder {folderId}");
            var versionIds = new List<string>();
            var itemsIDs = new List<string>();

            foreach (var item in contents)
            {
                string itemType = item.type.ToString();
                dynamic attributes = item.attributes;
                string name;

                System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Processing item type '{itemType}'");

                if (itemType == "folders")
                {
                    name = attributes.name;
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Found folder '{name}'");

                    if (name.Equals("pdf", StringComparison.OrdinalIgnoreCase))
                    {
                        System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Recursing into PDF folder '{name}'");
                        await GetPdfsInFolder(hubId, projectId, item.id.ToString(), tokens, pdfList);
                    }
                }
                else
                {
                    name = attributes.displayName;
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Found item '{name}'");

                    dynamic extension = attributes.extension;
                    string extensionType = extension.type;
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Item '{name}' has extension type '{extensionType}'");

                    if (extensionType == "items:autodesk.bim360:Document" || extensionType == "items:autodesk.bim360:File")
                    {
                        System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Processing BIM360 item '{name}' (type: {extensionType})");
                        string itemId = item.id.ToString();
                        string versionId = item.relationships.tip.data.id.ToString();

                        versionIds.Add(versionId);
                        itemsIDs.Add(itemId);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Skipping item '{name}' - extension type '{extensionType}' not supported");
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Collected {versionIds.Count} version IDs and {itemsIDs.Count} item IDs");

            if (versionIds.Count != 0)
            {
                System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Starting to process {versionIds.Count} versions");
                #region OldMethod

                //var versionTasks = new List<Task>();

                //for (int i = 0; i < versionIds.Count; i++)
                //{
                //    // Capture the current index for the closure in the task
                //    int index = i;

                //    // Add the task to the list
                //    versionTasks.Add(Task.Run(async () =>
                //    {
                //        await ProcessVersionDetailsAsync(projectId, itemsIDs[index], versionIds[index], tokens, pdfList);
                //    }));
                //}

                //// Await all tasks to complete processing in parallel
                //await Task.WhenAll(versionTasks);

                #endregion

                #region new Method

                //var semaphore = new SemaphoreSlim(2); // Limit concurrency
                //var tasks = new List<Task>();

                //for (int i = 0; i < versionIds.Count; i++)
                //{
                //    int index = i; // Capture the index for closure

                //    tasks.Add(Task.Run(async () =>
                //    {
                //        await semaphore.WaitAsync();
                //        try
                //        {
                //            await ProcessVersionDetailsAsync(projectId, itemsIDs[index], versionIds[index], tokens, pdfList);
                //        }
                //        catch (Exception ex)
                //        {
                //            Console.WriteLine($"Error processing version {versionIds[index]}: {ex.Message}");
                //        }
                //        finally
                //        {
                //            semaphore.Release();
                //        }
                //    }));
                //}

                //await Task.WhenAll(tasks);

                #endregion

                #region working Model
                //var semaphore = new SemaphoreSlim(Environment.ProcessorCount * 4); // Higher concurrency for IO-bound operations

                //// Pre-allocate the task list with known capacity
                //var tasks = new List<Task>(versionIds.Count);

                //const int batchSize = 100;
                //for (int i = 0; i < versionIds.Count; i += batchSize)
                //{
                //    int batchEnd = Math.Min(i + batchSize, versionIds.Count);

                //    for (int j = i; j < batchEnd; j++)
                //    {
                //        int index = j; // Capture the index for closure
                //        tasks.Add(Task.Run(async () =>
                //        {
                //            //try
                //            //{
                //            await semaphore.WaitAsync();
                //            try
                //            {
                //                await ProcessVersionDetailsAsync3(
                //                    projectId,
                //                    itemsIDs[index],
                //                    versionIds[index],
                //                    tokens,
                //                    pdfList
                //                ).ConfigureAwait(false); // Optimize thread usage                                
                //            }
                //            finally
                //            {
                //                semaphore.Release();
                //            }
                //            //}
                //            //catch (Exception ex)
                //            //{
                //            //    // Enhanced error logging
                //            //    Console.WriteLine($"Error processing version {versionIds[index]} at position {index}: {ex.Message}");
                //            //    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                //            //}
                //        }));
                //    }

                //    // Process each batch to avoid memory buildup
                //    if (tasks.Count >= batchSize)
                //    {
                //        await Task.WhenAll(tasks);
                //        tasks.Clear();
                //    }
                //}

                //// Process any remaining tasks
                //if (tasks.Count > 0)
                //{
                //    await Task.WhenAll(tasks);
                //}

                #region Working Code for all Sheets

                var semaphore = new SemaphoreSlim(25, 25); // Ensure only one call at a time
                int batchSize = 50; // Process 50 items at a time

                List<Task> tasks = new List<Task>();

                for (int i = 0; i < versionIds.Count; i += batchSize)
                {
                    int batchEnd = Math.Min(i + batchSize, versionIds.Count);

                    // Process the current batch
                    for (int j = i; j < batchEnd; j++)
                    {
                        int index = j; // Capture the index for closure
                        tasks.Add(Task.Run(async () =>
                        {
                            await semaphore.WaitAsync();
                            try
                            {
                                System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Processing version {index + 1}/{versionIds.Count} - ID: {versionIds[index]}");
                                await ProcessVersionDetailsAsync(
                                    projectId,
                                    itemsIDs[index],
                                    versionIds[index],
                                    tokens,
                                    pdfList
                                ).ConfigureAwait(false); // Optimize thread usage
                                System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Completed processing version {index + 1}/{versionIds.Count}");
                            }
                            finally
                            {
                                semaphore.Release();
                            }
                        }));
                    }

                    // Wait for all tasks in the current batch to complete
                    await Task.WhenAll(tasks).ConfigureAwait(false);

                    // Clear the tasks list for the next batch
                    tasks.Clear();

                    // Introduce a 30-second delay after every 50 items
                    if (batchEnd < versionIds.Count) // Avoid delay after the last batch
                    {
                        await Task.Delay(TimeSpan.FromSeconds(30)).ConfigureAwait(false);
                    }
                }

                #endregion

                #endregion
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: No version IDs collected - no PDFs to process");
            }

            System.Diagnostics.Debug.WriteLine($"[DEBUG] GetPdfsInFolder: Method completed - pdfList now contains {pdfList.Count} items");
        }

        private async Task ProcessVersionDetailsAsync(string projectId, string itemId, string versionId, Tokens tokens, List<string> pdfList)
        {
            System.Diagnostics.Debug.WriteLine($"[DEBUG] ProcessVersionDetailsAsync: Starting for versionId: {versionId}");

            // Retrieve batch version details
            dynamic versionsMetadata = await _apsService.GetBatchVersionDetails2(projectId, itemId, versionId, tokens);
            System.Diagnostics.Debug.WriteLine($"[DEBUG] ProcessVersionDetailsAsync: GetBatchVersionDetails2 returned: {(versionsMetadata != null ? "data" : "null")}");

            if (versionsMetadata != null)
            {
                System.Diagnostics.Debug.WriteLine($"[DEBUG] ProcessVersionDetailsAsync: Processing {versionsMetadata.Count} version metadata items");
                //Check for nulls
                foreach (var version in versionsMetadata)
                {
                    string name = version.name;
                    string revision = "Revision not found";
                    string sheetSize = "Sheet Size not found";
                    string description = "Description not found";

                    // Access custom attributes
                    var customAttributes = version.customAttributes;

                    if (customAttributes != null)
                    {
                        foreach (var attribute in customAttributes)
                        {
                            string namer = attribute.name;

                            switch (namer)
                            {
                                case "Revision":
                                    revision = attribute.value.ToString();
                                    break;
                                case "REVISION":
                                    revision = attribute.value.ToString();
                                    break;
                                case "Rev":
                                    revision = attribute.value.ToString();
                                    break;
                                case "REV":
                                    revision = attribute.value.ToString();
                                    break;
                                case "Sheet Size":
                                    sheetSize = attribute.value.ToString();
                                    break;
                                case "sheet Size":
                                    sheetSize = attribute.value.ToString();
                                    break;
                                case "Sheet size":
                                    sheetSize = attribute.value.ToString();
                                    break;
                                case "SHEET SIZE":
                                    sheetSize = attribute.value.ToString();
                                    break;
                                case "Desc":
                                    description = attribute.value.ToString();
                                    break;
                                case "DESC":
                                    description = attribute.value.ToString();
                                    break;
                            }
                        }

                        // Add the result to pdfList in a thread-safe manner
                        lock (pdfList)
                        {
                            //One Rosebank

                            string FinalName = name.Replace(",", "");
                            string FinalDescription = description.Replace(",", "");
                            string FinalRevision = revision.Replace(",", "");
                            string FinalSheetSize = sheetSize.Replace(",", "");

                            System.Diagnostics.Debug.WriteLine($"[DEBUG] ProcessVersionDetailsAsync: PDF details - Name: '{FinalName}', Description: '{FinalDescription}', Revision: '{FinalRevision}', SheetSize: '{FinalSheetSize}'");

                            if (FinalDescription != "Description not found")
                            {
                                pdfList.Add($"{FinalName},{FinalDescription},{FinalRevision},{FinalSheetSize}");
                                System.Diagnostics.Debug.WriteLine($"[DEBUG] ProcessVersionDetailsAsync: Added PDF to list: {FinalName}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"[DEBUG] ProcessVersionDetailsAsync: SKIPPED PDF '{FinalName}' - Description not found");
                            }
                        }
                    }
                }
            }
        }

        private async Task GetPdfsInFolder3(string hubId, string projectId, string folderId, Tokens tokens, List<dynamic> pdfList)
        {
            var autodeskService = new AutodeskService(new HttpClient());

            // We use a queue to avoid deep recursion
            var folderQueue = new Queue<string>();
            folderQueue.Enqueue(folderId);

            var versionIds = new List<string>();
            var itemsIDs = new List<string>();

            // Loop through folders using a queue, similar to paginating through results in JavaScript
            while (folderQueue.Count > 0)
            {
                var currentFolderId = folderQueue.Dequeue();
                var contents = await autodeskService.GetFolderContentsAsync(hubId, projectId, currentFolderId, tokens.InternalToken);

                foreach (var item in contents)
                {
                    string itemType = item.type.ToString();
                    dynamic attributes = item.attributes;
                    string name = attributes.displayName.ToString();
                    dynamic extension = attributes.extension;
                    string extensionType = extension.type.ToString();

                    //items: autodesk.bim360:Document
                    //items:autodesk.bim360:File

                    if (itemType == "folders")
                    {
                        // Enqueue folders for later processing
                        folderQueue.Enqueue(item.id.ToString());
                    }
                    else if (extensionType == "items:autodesk.bim360:Document") //&& name.EndsWith(".pdf"))
                    {
                        // Fetch the versions of the item
                        var versions = await _apsService.GetVersions2(hubId, projectId, item.id.ToString(), tokens);

                        if (versions != null && versions.Count > 0)
                        {
                            var firstVersion = versions[0];

                            versionIds.Add(firstVersion.LineageUrn);
                            itemsIDs.Add(firstVersion.ID);

                            pdfList.Add(item);  // Add the item to the pdf list
                        }
                    }
                }
            }

            if (versionIds.Count > 0)
            {
                for (int i = 0; i < versionIds.Count; i++)
                {
                    var versionsMetadata = await _apsService.GetBatchVersionDetails2(projectId, itemsIDs[i], versionIds[i], tokens);

                    foreach (var version in versionsMetadata)
                    {
                        // Process custom attributes similar to JavaScript filtering and handling
                        var customAttributes = version.customAttributes as Dictionary<string, object>;

                        if (customAttributes != null && customAttributes.TryGetValue("SheetNumber", out var sheetNumber))
                        {
                            var description = customAttributes.ContainsKey("Description") ? customAttributes["Description"].ToString() : "Description not found";
                            var revision = customAttributes.ContainsKey("Revision") ? customAttributes["Revision"].ToString() : "Revision not found";

                            pdfList.Add(new
                            {
                                SheetName = sheetNumber.ToString(),
                                Description = description,
                                Revision = revision,
                                FileName = version.name,
                                Title = version.title,
                                CreateTime = version.createTime,
                                LastModifiedTime = version.lastModifiedTime,
                                RevisionNumber = version.revisionNumber
                            });
                        }
                    }
                }
            }
        }

        private async Task ProcessVersionDetailsAsync1(string projectId, string itemId, string versionId, Tokens tokens, List<string> pdfList)
        {
            try
            {
                // Retrieve batch version details
                dynamic versionsMetadata = await _apsService.GetBatchVersionDetails2(projectId, itemId, versionId, tokens);
                if (versionsMetadata == null) return;

                // Define attribute mappings
                var revisionKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Revision", "REVISION", "Rev", "REV"
        };

                var sheetSizeKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Sheet Size", "sheet Size", "Sheet size", "SHEET SIZE"
        };

                var descriptionKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "Desc", "DESC"
        };

                foreach (var version in versionsMetadata)
                {
                    if (version == null) continue;

                    // Correctly access JObject properties
                    string name = version["name"]?.ToString() ?? string.Empty;
                    string revision = "Revision not found";
                    string sheetSize = "Sheet Size not found";
                    string description = "Description not found";

                    // Process custom attributes if they exist
                    var customAttributes = version["customAttributes"];
                    if (customAttributes != null)
                    {
                        foreach (var attribute in customAttributes)
                        {
                            if (attribute == null) continue;

                            // Correctly access JObject properties
                            string attrName = attribute["name"]?.ToString();
                            string attrValue = attribute["value"]?.ToString();

                            if (string.IsNullOrEmpty(attrName) || string.IsNullOrEmpty(attrValue))
                                continue;

                            if (revisionKeywords.Contains(attrName))
                            {
                                revision = attrValue;
                            }
                            else if (sheetSizeKeywords.Contains(attrName))
                            {
                                sheetSize = attrValue;
                            }
                            else if (descriptionKeywords.Contains(attrName))
                            {
                                description = attrValue;
                            }
                        }

                        // Only process if we have a meaningful description
                        if (description != "Description not found")
                        {
                            // Clean and format the data
                            var cleanedData = new string[]
                            {
                        name.Replace(",", ""),
                        description.Replace(",", ""),
                        revision.Replace(",", ""),
                        sheetSize.Replace(",", "")
                            };

                            // Add to list in thread-safe manner
                            lock (pdfList)
                            {
                                pdfList.Add(string.Join(",", cleanedData));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing version {versionId}: {ex.Message}");
                throw;
            }
        }

        private async Task ProcessVersionDetailsAsync2(string projectId, string itemId, string versionId, Tokens tokens, List<string> pdfList)
        {
            // Retrieve batch version details
            dynamic versionsMetadata = await _apsService.GetBatchVersionDetails2(projectId, itemId, versionId, tokens);

            if (versionsMetadata != null)
            {
                foreach (var version in versionsMetadata)
                {
                    // Safely retrieve 'name' (if it exists)
                    string name = version?.name != null ? version.name.ToString() : "Name not found";

                    string revision = "Revision not found";
                    string sheetSize = "Sheet Size not found";
                    string description = "Description not found";

                    // Access custom attributes (if they exist)
                    var customAttributes = version?.customAttributes;
                    if (customAttributes != null)
                    {
                        foreach (var attribute in customAttributes)
                        {
                            string namer = attribute?.name != null ? attribute.name.ToString() : string.Empty;
                            string value = attribute?.value != null ? attribute.value.ToString() : string.Empty;

                            switch (namer)
                            {
                                case "Revision":
                                case "REVISION":
                                case "Rev":
                                case "REV":
                                    revision = value;
                                    break;
                                case "Sheet Size":
                                case "sheet Size":
                                case "Sheet size":
                                case "SHEET SIZE":
                                    sheetSize = value;
                                    break;
                                case "Description":
                                case "DESCRIPTION":
                                case "Desc":
                                case "desc":
                                    description = value;
                                    break;
                            }
                        }
                    }

                    // Log or process extracted values
                    Console.WriteLine($"Name: {name}, Revision: {revision}, Sheet Size: {sheetSize}, Description: {description}");
                }
            }
        }

        private async Task ProcessVersionDetailsAsync3(string projectId, string itemId, string versionId, Tokens tokens, List<string> pdfList)
        {
            // Retrieve batch version details
            JToken versionsMetadata = await _apsService.GetBatchVersionDetails3(projectId, itemId, versionId, tokens);

            if (versionsMetadata.Type == JTokenType.Object)
            {
                Console.WriteLine("Returned JSON is a JObject");
                JObject jsonObject = (JObject)versionsMetadata;

                // Process as JObject
                JArray versions1 = jsonObject["results"] as JArray ?? jsonObject["versions"] as JArray;

                if (versions1 != null)
                {
                    foreach (JToken version in versions1)
                    {
                        // Safely retrieve 'name'
                        string name = version["name"]?.ToString() ?? "Name not found";

                        string revision = "Revision not found";
                        string sheetSize = "Sheet Size not found";
                        string description = "Description not found";

                        // Access custom attributes safely
                        JArray customAttributes = version["customAttributes"] as JArray;
                        if (customAttributes != null)
                        {
                            foreach (JToken attribute in customAttributes)
                            {
                                string namer = attribute["name"]?.ToString();
                                string value = attribute["value"]?.ToString();

                                if (namer != null && value != null)
                                {
                                    switch (namer.ToUpperInvariant())
                                    {
                                        case "REVISION":
                                        case "REV":
                                            revision = value;
                                            break;
                                        case "SHEET SIZE":
                                            sheetSize = value;
                                            break;
                                    }
                                }
                            }
                        }

                        // Print values for debugging
                        Console.WriteLine($"Name: {name}, Revision: {revision}, Sheet Size: {sheetSize}");
                    }
                }
            }
            else if (versionsMetadata.Type == JTokenType.Array)
            {
                Console.WriteLine("Returned JSON is a JArray");
                JArray jsonArray = (JArray)versionsMetadata;
            }
        }

        private async Task<List<string>> GetPdfsInFolder1(string hubId, string projectId, string folderId, Tokens tokens)
        {
            List<string> pdfList = new List<string>();

            var contents = await _apsService.GetContents(hubId, projectId, folderId, tokens);

            foreach (var item in contents)
            {
                string itemType = item.type.ToString();
                dynamic attributes = item.attributes;
                string name;

                if (itemType == "folders")
                {
                    name = attributes.name;
                    await GetPdfsInFolder(hubId, projectId, item.id.ToString(), tokens, pdfList);
                }
                else
                {
                    name = attributes.displayName;
                    dynamic extension = attributes.extension;
                    string extensionType = extension.type;

                    if (extensionType == "items:autodesk.bim360:File" && name.EndsWith(".pdf"))
                    {
                        pdfList.Add(item);
                    }
                }
            }

            return pdfList;
        }

        private async Task<Stream> GetPdfStream(dynamic item, Tokens tokens)
        {
            // Check for the storage link in the item structure
            if (item.relationships != null && item.relationships.storage != null && item.relationships.storage.meta != null && item.relationships.storage.meta.link != null)
            {
                string downloadUrl = item.relationships.storage.meta.link.href;

                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokens.InternalToken);
                    var response = await client.GetAsync(downloadUrl);
                    response.EnsureSuccessStatusCode();
                    return await response.Content.ReadAsStreamAsync();
                }
            }
            else
            {
                // Log the item structure for debugging purposes
                Console.WriteLine("Item structure: " + Newtonsoft.Json.JsonConvert.SerializeObject(item));
                throw new Exception("Storage link not found in item.");
            }
        }

        public async Task<List<dynamic>> GetTheSheetSizeMethod(string hubId, string projectId, dynamic item, Tokens tokens)
        {
            dynamic versions = await _apsService.GetVersions(hubId, projectId, item.id.ToString(), tokens);

            if (versions.Count > 0)
            {
                var latestVersion = versions[0];
                string urn = Base64Encode(latestVersion.id.ToString());

                string jobId = await TranslatePdf(urn, tokens);

                bool translationComplete = false;
                while (!translationComplete)
                {
                    await Task.Delay(5000);
                    dynamic jobStatus = await GetJobStatus(urn, tokens);
                    translationComplete = jobStatus.status == "success";
                }

                var sheetMetadata = await _apsService.GetSheetMetadata(urn, tokens);

                item.SheetMetadata = sheetMetadata;

                return sheetMetadata;
            }

            return null;
        }

        //Specific folder
        public async Task<List<string>> GetPdfsFromSpecificFolders(string hubId, string projectId, string rootFolderId, Tokens tokens)
        {
            var pdfList = new List<string>();

            // Find the "CIVIL" folder
            string civilFolderId = await GetFolderIdByName(hubId, projectId, rootFolderId, "CIVIL", tokens);
            if (!string.IsNullOrEmpty(civilFolderId))
            {
                await GetPdfsInFolder(hubId, projectId, civilFolderId, tokens, pdfList);
            }

            // Find the "STRUCTURAL" folder
            string structuralFolderId = await GetFolderIdByName(hubId, projectId, rootFolderId, "STRUCTURAL", tokens);
            if (!string.IsNullOrEmpty(structuralFolderId))
            {
                await GetPdfsInFolder(hubId, projectId, structuralFolderId, tokens, pdfList);
            }

            return pdfList;
        }

        private async Task<string> GetFolderIdByName(string hubId, string projectId, string parentFolderId, string folderName, Tokens tokens)
        {
            var contents = await _apsService.GetContents(hubId, projectId, parentFolderId, tokens);

            foreach (var item in contents)
            {
                if (item.type.ToString() == "folders" && item.attributes.name.ToString().Equals(folderName, StringComparison.OrdinalIgnoreCase))
                {
                    return item.id.ToString();
                }
            }

            return null; // Return null if the folder is not found
        }

        public async Task<ActionResult> ShowSelectedPdfs(List<string> selectedPdfs, string projectName, string hubId, string projectId)
        {

            Tokens tokens = Session["tokens"] as Tokens;
            if (tokens == null)
            {
                return RedirectToAction("Login", "Auth");
            }

            if (tokens.ExpiresAt <= DateTime.UtcNow)
            {
                return RedirectToAction("RefreshToken", "Auth");
            }

            if (selectedPdfs == null || selectedPdfs.Count == 0)
            {
                TempData["Message"] = "No PDFs selected.";
                return RedirectToAction("GetAllPdfs", new { hubId = hubId, projectId = projectId, projectName = projectName });
            }

            var companies = await _companyRepository.GetAllCompaniesAsync();

            var persons = new Person();

            var listOfperson = persons.ListAll();

            List<CompanyModel> companyModels = new List<CompanyModel>();

            var companiesList = new Company();

            var companieser = companiesList.ListAll();

            int count = 0;

            foreach (var company in companieser)
            {
                var companymodelinner = new CompanyModel
                {
                    Company = company.Name,
                    Person = listOfperson.Count > count && listOfperson[count].Name != null ? listOfperson[count].Name : "",
                    _id = "",
                    Date = ""
                };

                companyModels.Add(companymodelinner);
                count++;
            }

            var model = new CreateTransmittalViewModel
            {
                SelectedPdfs = selectedPdfs,
                Companies = companyModels,
                ProjectName = projectName,
                hubID = hubId,
                projectId = projectId
            };

            return View("CreateTransmittal", model);
        }

        private string Base64Encode(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        private async Task<string> TranslatePdf(string urn, Tokens tokens)
        {
            var derivativeApi = new DerivativesApi();
            var jobPayload = new JobPayload(new JobPayloadInput(urn), new JobPayloadOutput(new List<JobPayloadItem>
            {
                new JobPayloadItem(JobPayloadItem.TypeEnum.Svf, new List<JobPayloadItem.ViewsEnum> { JobPayloadItem.ViewsEnum._2d, JobPayloadItem.ViewsEnum._3d })
            }));

            derivativeApi.Configuration.AccessToken = tokens.InternalToken;
            var job = await derivativeApi.TranslateAsync(jobPayload, true);
            return job.result;
        }

        private async Task<dynamic> GetJobStatus(string urn, Tokens tokens)
        {
            var derivativeApi = new DerivativesApi();
            derivativeApi.Configuration.AccessToken = tokens.InternalToken;
            var jobStatus = await derivativeApi.GetManifestAsync(urn);
            return jobStatus;
        }

        public async Task<ActionResult> GetSheetSizes(string urn)
        {
            Tokens tokens = Session["tokens"] as Tokens;
            if (tokens == null)
            {
                return RedirectToAction("Login", "Auth");
            }

            var sheetMetadata = await _apsService.GetSheetMetadata(urn, tokens);
            return Json(sheetMetadata, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public async Task<ActionResult> ViewTheCreatedTransmittal(List<string> selectedPdfs, string selectedcompany, string selectedperson, string selectedstatus, string ProjectName, string projectId, string hubID)
        {
            // DEBUG: Entry point logging
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewTheCreatedTransmittal ACTION ENTRY - ProjectName='{ProjectName}', projectId='{projectId}', hubID='{hubID}'");
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewTheCreatedTransmittal FORM DATA - selectedcompany='{selectedcompany}', selectedperson='{selectedperson}', selectedstatus='{selectedstatus}'");
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewTheCreatedTransmittal SELECTED PDFS - Count={selectedPdfs?.Count ?? 0}");

            // DEBUG: Log current Session state before setting TempData
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewTheCreatedTransmittal SESSION STATE (BEFORE) - CurrentHubId='{Session["CurrentHubId"]}', CurrentProjectId='{Session["CurrentProjectId"]}', CurrentProjectName='{Session["CurrentProjectName"]}'");

            TempData["SelectedPdfs"] = selectedPdfs;
            TempData["selectedcompany"] = selectedcompany;
            TempData["selectedperson"] = selectedperson;
            TempData["selectedstatus"] = selectedstatus;
            TempData["projectId"] = projectId;
            TempData["hubID"] = hubID;

            // DEBUG: Log TempData after setting
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewTheCreatedTransmittal TEMPDATA SET - hubID='{TempData["hubID"]}', projectId='{TempData["projectId"]}', SelectedPdfs count={(TempData["SelectedPdfs"] as List<string>)?.Count ?? 0}");

            Tokens tokens = Session["tokens"] as Tokens;
            if (tokens == null)
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewTheCreatedTransmittal ERROR - No tokens found in session, redirecting to Login");
                return RedirectToAction("Login", "Auth");
            }

            if (tokens.ExpiresAt <= DateTime.UtcNow)
            {
                System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewTheCreatedTransmittal ERROR - Tokens expired at {tokens.ExpiresAt}, redirecting to RefreshToken");
                return RedirectToAction("RefreshToken", "Auth");
            }

            // DEBUG: Log token state
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] ViewTheCreatedTransmittal TOKENS - Valid until {tokens.ExpiresAt}");

            var AllcorrectTransDataWithLargestRevision = new List<SheetTransmittalData>();

            TransmittalRespository transmittal = new TransmittalRespository();

            var transmittalsForThisProject = transmittal.ListAllTransmittalsFromProject(ProjectName);

            string transmittalNo = string.Empty;
            string CompanyTransmittalNo = string.Empty;


            //Get the transmittal No
            transmittalNo = (transmittalsForThisProject.Count + 1).ToString();

            var transmittalForProjectAndCompany = transmittal.ListAllTransmittalsFromProjectAndCompany(ProjectName, selectedcompany);

            //Get The Company transmittal No
            CompanyTransmittalNo = (transmittalForProjectAndCompany.Count + 1).ToString();

            #region GetMetaData

            //GetTheMetaData metaData = new GetTheMetaData();
            //metaData.RunData();

            #endregion

            //Filter the name so that its detectable in the database
            //If the name does not exist create a new sheet with sheetSize
            //Get the sheet size from bim360
            //Get the project name to recieve the transmittal number
            //Get the no to know what number is next
            //get all the projects for that company and get the highest transmittal no

            #region MongoDbMethod


            //if (selectedPdfs.Count != 0)
            //{
            //    foreach (var item in selectedPdfs)
            //    {
            //        bool DocNameEqualsDataName = false;
            //        int highestRevision = 0;
            //        List<SheetTransmittalData> dataForDoc = new List<SheetTransmittalData>();

            //        //This will make a model for all the pdfs with the same name
            //        //Get the one with highest revision
            //        var data = await _mongoDBManager.LoadLatestSheetRevisionAndSheetsizeAsync(_mongoDBManager.ConnectToDatabase(), item); //"2021_0108-01-R-1000");

            //        //Gets all the pdfs in the database
            //        if (data.Count != 0)
            //        {
            //            //Filters and gets all the PDFs
            //            foreach (var item1 in data)
            //            {

            //                //Foreach of those documents
            //                DocNameEqualsDataName = false;
            //                for (int i = 0; i <= item1.DocNo.Count - 1; i++)
            //                {
            //                    if (item1.DocNo[i] == item)
            //                    {
            //                        DocNameEqualsDataName = true;
            //                        //item1.Revision[i]
            //                        //check the sheet size if its not the same size skip
            //                        string sheetsizeFilter = "";
            //                        string descritpionFilter = "";
            //                        object revisionFilter = "0";

            //                        if (item1.SheetSize.Count == item1.DocNo.Count)
            //                        {
            //                            sheetsizeFilter = item1.SheetSize[i];
            //                        }

            //                        if (item1.Description.Count == item1.DocNo.Count)
            //                        {
            //                            descritpionFilter = item1.Description[i];
            //                        }

            //                        if (item1.Revision.Count == item1.DocNo.Count)
            //                        {
            //                            revisionFilter = item1.Revision[i];
            //                        }

            //                        var rev = new SheetTransmittalData
            //                        {
            //                            DocNo = item1.DocNo[i],
            //                            Description = descritpionFilter,
            //                            Revision = revisionFilter,
            //                            SheetSize = sheetsizeFilter
            //                        };

            //                        dataForDoc.Add(rev);
            //                    }
            //                }
            //            }

            //            //Check if it enterd the doc Name and sheet selected name are equal
            //            if (DocNameEqualsDataName)
            //            {
            //                highestRevision = dataForDoc.Max(d => int.TryParse(d.Revision.ToString(), out int rev) ? rev : 0);

            //                var CorrectData = new SheetTransmittalData
            //                {
            //                    DocNo = dataForDoc.First().DocNo,
            //                    Description = dataForDoc.First().Description,
            //                    Revision = highestRevision,
            //                    SheetSize = dataForDoc.First().SheetSize
            //                };

            //                AllcorrectTransDataWithLargestRevision.Add(CorrectData);
            //            }
            //        }
            //        else
            //        {
            //            //if its new and its not there in the database,
            //            //We must add it completely
            //        }
            //    }
            //}

            //List<string> AllDocNo = new List<string>();
            //List<string> AllDescription = new List<string>();
            //List<object> AllRevision = new List<object>();
            //List<string> AllSheetSize = new List<string>();

            ////Adds all the data to the display modal
            //foreach (var item in AllcorrectTransDataWithLargestRevision)
            //{
            //    AllDocNo.Add(item.DocNo);
            //    AllDescription.Add(item.Description);
            //    AllRevision.Add(item.Revision);
            //    AllSheetSize.Add(item.SheetSize);
            //}


            //var modal = new TransmitallCreationDataViewModal
            //{

            //    DocNo = AllDocNo,
            //    Description = AllDescription,
            //    Revision = AllRevision,
            //    SheetSize = AllSheetSize,
            //    companyname = selectedcompany,
            //    personName = selectedperson,

            //    //Get The No And Transmittal No

            //    No = "0",
            //    TransmittalNo = "0",
            //    Status = selectedstatus
            //};


            ////var modal = new ViewTransmittalViewModal
            ////{
            ////    SelectedPDF = selectedPdfs,
            ////    selectCompany = selectedcompany,
            ////    selectPerson = selectedperson,
            ////    selectStatus = selectedstatus
            ////};

            #endregion

            //SQL Method
            //Filter the name so that its detectable in the database
            //If the name does not exist create a new sheet with sheetSize
            //Get the sheet size from bim360
            //Get the project name to recieve the transmittal number
            //Get the no to know what number is next
            //get all the projects for that company and get the highest transmittal no            

            // List<string> revisionOrder = new List<string> { "A", "B", "C", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10" };
            var revEntryTransmittal = new DocRevTransmittal();

            List<string> AllDocNo = new List<string>();
            List<string> AllDescription = new List<string>();
            List<string> AllRevision = new List<string>();
            List<string> AllSheetSize = new List<string>();

            // Loads All The Selected PDFs
            foreach (var sheets in selectedPdfs)
            {
                var sheetsSpilt = sheets.Split(',');

                //Separate Name And Description
                //Sheets sheet = new Sheets();
                //var SheetLoaded = sheet.LoadName(sheetsSpilt[0]);
                //string Sheetrevision = sheetsSpilt[2];

                #region Old Method
                //if (SheetLoaded.Count > 0)
                //{
                //    HashSet<string> uniqueRevisions = new HashSet<string>();
                //    List<DocRevTransmittal.Item> revEntries = new List<DocRevTransmittal.Item>();
                //    string SheetName = string.Empty;
                //    string SheetDescription = string.Empty;
                //    string SheetSize = string.Empty;
                //    int revEntriesCount = 0;

                //    SheetName = SheetLoaded.FirstOrDefault().Name;

                //    SheetDescription = SheetLoaded.FirstOrDefault().Description;

                //    SheetSize = SheetLoaded.FirstOrDefault().SheetSize;

                //    //Must be used for numbers
                //    #region Sheet

                //    //foreach (var item in SheetLoaded)
                //    //{
                //    //    SheetName = item.Name;

                //    //    SheetDescription = item.Description;

                //    //    SheetSize = item.SheetSize;

                //    //    //This is what changes                      
                //    //    revEntries = revEntryTransmittal.ListAllWhereSheetID(item.Id.ToString());

                //    //    //The rev does not have a unique entry
                //    //    if (revEntries.Count != 0)
                //    //    {
                //    //        foreach (var rev in revEntries)
                //    //        {
                //    //            uniqueRevisions.Add(rev.RevNo);
                //    //        }
                //    //    }
                //    //    else
                //    //    {
                //    //        uniqueRevisions.Add("0");
                //    //    }

                //    //    revEntriesCount = revEntries.Count;
                //    //}

                //    //string LargestRev = string.Empty;

                //    //if (revEntriesCount != 0)
                //    //{

                //    //    // Sort the unique revisions according to the desired order
                //    //    List<string> sortedRevisions = uniqueRevisions
                //    //        .Where(rev => revisionOrder.Contains(rev))
                //    //        .OrderBy(rev => revisionOrder.IndexOf(rev))
                //    //        .ToList();

                //    //    LargestRev = sortedRevisions.LastOrDefault();

                //    //    switch (LargestRev)
                //    //    {
                //    //        case "A":
                //    //            LargestRev = "B";
                //    //            break;
                //    //        case "B":
                //    //            LargestRev = "C";
                //    //            break;
                //    //        case "C":
                //    //            LargestRev = "0";
                //    //            break;
                //    //        case "0":
                //    //            LargestRev = "1";
                //    //            break;
                //    //        case "1":
                //    //            LargestRev = "2";
                //    //            break;
                //    //        case "2":
                //    //            LargestRev = "3";
                //    //            break;
                //    //        case "3":
                //    //            LargestRev = "4";
                //    //            break;
                //    //        case "4":
                //    //            LargestRev = "5";
                //    //            break;
                //    //        case "5":
                //    //            LargestRev = "6";
                //    //            break;
                //    //        case "6":
                //    //            LargestRev = "7";
                //    //            break;
                //    //        case "7":
                //    //            LargestRev = "8";
                //    //            break;
                //    //        case "8":
                //    //            LargestRev = "9";
                //    //            break;
                //    //        case "9":
                //    //            LargestRev = "10";
                //    //            break;
                //    //        case "10":
                //    //            LargestRev = "11";
                //    //            break;
                //    //        case "11":
                //    //            LargestRev = "12";
                //    //            break;
                //    //    }
                //    //}
                //    //else
                //    //{
                //    //    LargestRev = "0";
                //    //}
                //    #endregion

                //    AllDocNo.Add(SheetName);
                //    AllDescription.Add(RemoveRev(SheetDescription));
                //    AllRevision.Add(Sheetrevision);

                //    if (SheetSize == "" || SheetSize == " " || string.IsNullOrEmpty(SheetSize))
                //    {
                //        AllSheetSize.Add("A0");
                //    }
                //    else
                //    {
                //        AllSheetSize.Add(SheetSize);
                //    }
                //}
                //else
                //{
                //    //if the pdf is not in the database i need to add it and make it show on the transmittal.

                //    //To Get Bim360 data

                //    // Load From BIM 360 the sheet details
                //    //var documentDetails = await GetDocumentDetailsFromBIM360(hubID, projectId, sheets, tokens);

                //    //if (documentDetails != null)
                //    //{
                //    //    string SheetName = documentDetails.name;
                //    //    string SheetDescription = documentDetails.description;
                //    //    string SheetSize = documentDetails.sheetSize;

                //    //    // Add the new sheet to the database (if necessary)
                //    //    Sheets newSheet = new Sheets
                //    //    {
                //    //        Name = SheetName,
                //    //        Description = SheetDescription,
                //    //        SheetSize = SheetSize
                //    //        // Add other necessary properties
                //    //    };
                //    //    newSheet.Save(); // Assuming a Save method to insert the new sheet into the database

                //    //    AllDocNo.Add(SheetName);
                //    //    AllDescription.Add(SheetDescription);
                //    //    AllRevision.Add("0"); // Assuming initial revision is "0"
                //    //    AllSheetSize.Add(SheetSize);
                //    //}
                //    //else
                //    //{

                //    AllDocNo.Add(sheetsSpilt[0]);

                //    if (sheetsSpilt[1] != null && !string.IsNullOrEmpty(sheetsSpilt[1]))
                //    {
                //        AllDescription.Add(sheetsSpilt[1]);
                //    }

                //    AllRevision.Add(sheetsSpilt[2]);
                //    AllSheetSize.Add("A0");

                //    //}
                //}
                #endregion

                AllDocNo.Add(sheetsSpilt[0]);

                if (sheetsSpilt[1] != null && !string.IsNullOrEmpty(sheetsSpilt[1]))
                {
                    AllDescription.Add(sheetsSpilt[1]);
                }

                AllRevision.Add(sheetsSpilt[2]);
                AllSheetSize.Add(sheetsSpilt[3]);
            }

            var modal = new TransmitallCreationDataViewModal1
            {
                DocNo = AllDocNo,
                Description = AllDescription,
                Revision = AllRevision,
                SheetSize = AllSheetSize,
                companyname = selectedcompany,
                personName = selectedperson,
                ProjectName = ProjectName,
                projectID = projectId,

                //Get The No And Transmittal No
                No = CompanyTransmittalNo,
                TransmittalNo = transmittalNo,
                Status = selectedstatus
            };

            #region Memories

            //if (!int.TryParse(model.Id, out int transmittalId))
            //{
            //    //return BadRequest("Invalid Transmittal ID.");
            //}

            //var revEntryTransmittal = new DocRevTransmittal();
            //var revEntries = await revEntryTransmittal.LoadAllByTransidAsync(transmittalId);
            //if (revEntries == null)
            //{
            //    //return NotFound("No RevEntries found for the specified Transmittal ID.");
            //}

            // Load Transmittal
            //var transmittal = new Transmittal();
            //var loadedTransmittal = transmittal.LoadData(transmittalId);
            //if (loadedTransmittal == null)
            //{
            //    //return NotFound("Transmittal not found.");
            //}

            // transmittal.UpdateTransNoAndDate(int.Parse(model.Id), model.TransNo, model.Date);

            //var sheets = new Sheets();
            //foreach (var items in revEntries)
            //{
            //    var sheet = sheets.LoadData(int.Parse(items.DocId));

            //    foreach (var item in model.DisplayFormats)
            //    {
            //        if (int.Parse(items.DocId) == item.Id)
            //        {
            //            revEntryTransmittal.UpdateRevNoAndDate(items.Id, item.Revision.ToString(), model.Date);
            //        }

            //        sheets.Name = item.DocumentNo;
            //        sheets.Description = item.Description;
            //        sheets.SheetSize = item.SheetSize;

            //        //Saves all the changes on the sheet
            //        if (sheets.SaveAll())
            //        {

            //        }
            //        else
            //        {

            //        }
            //    }
            //}

            #endregion

            return View(modal);
        }

        private async Task<string> GetDocumentDetailsFromBIM360(string hubId, string projectId, string sheetName, Tokens tokens)
        {
            //The PDFs already come from bim360 all i need is the pdf id to check.

            //var topFolders = await _apsService.GetContents(hubId, projectId, null, tokens);
            //var allPdfs = new List<string>();

            //foreach (var folder in topFolders)
            //{
            //    await GetPdfsInFolder(hubId, projectId, folder["id"].ToString(), tokens, allPdfs);
            //}

            //foreach (var pdf in allPdfs)
            //{
            //    dynamic attributes = pdf.attributes;
            //    string name = attributes.displayName;

            //    if (name.Equals(sheetName, StringComparison.OrdinalIgnoreCase))
            //    {
            //        var versions = await _apsService.GetVersions(hubId, projectId, pdf.id.ToString(), tokens);
            //        var latestVersion = versions.FirstOrDefault();

            //        if (latestVersion != null)
            //        {
            //            dynamic latestAttributes = latestVersion.attributes;
            //            return new
            //            {
            //                name = latestAttributes.displayName,
            //                description = latestAttributes.description ?? "No description available",
            //                sheetSize = latestAttributes.extension.data.sheetSize ?? "Unknown"
            //            };
            //        }
            //    }
            //}

            return null;
        }

        public ActionResult CreateTransmittal()
        {
            // DEBUG: Entry point logging
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] CreateTransmittal ACTION ENTRY");

            // DEBUG: Log current Session state
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] CreateTransmittal SESSION STATE - CurrentHubId='{Session["CurrentHubId"]}', CurrentProjectId='{Session["CurrentProjectId"]}', CurrentProjectName='{Session["CurrentProjectName"]}'");

            // DEBUG: Log current TempData state before retrieval
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] CreateTransmittal TEMPDATA STATE (BEFORE) - hubId='{TempData["hubId"]}', projectId='{TempData["projectId"]}', SelectedPdfs='{TempData["SelectedPdfs"]}'");

            var selectedPdfs = TempData["SelectedPdfs"] as List<string>;
            var hubId = TempData["hubId"] as string;
            var projectId = TempData["projectId"] as string;

            // DEBUG: Log retrieved values
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] CreateTransmittal RETRIEVED VALUES - hubId='{hubId}', projectId='{projectId}', selectedPdfs count={selectedPdfs?.Count ?? 0}");

            // DEBUG: Log current TempData state after retrieval
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] CreateTransmittal TEMPDATA STATE (AFTER) - hubId='{TempData["hubId"]}', projectId='{TempData["projectId"]}', SelectedPdfs='{TempData["SelectedPdfs"]}'");
            System.Diagnostics.Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] CreateTransmittal ACTION EXIT");

            return View(selectedPdfs);
        }

        [HttpPost]
        public ActionResult IssueTransmittal(TransmitallCreationDataViewModal1 model)
        {
            //Save The Transmittal
            DataAccess.Project project = new DataAccess.Project();
            var LoadedProject = project.ListAll();
            bool exists = false;

            TransmittalRespository transmittal = new TransmittalRespository();

            Company company = new Company();
            var _companyID = company.LoadByName(model.companyname);

            Person person = new Person();

            var _personID = person.LoadbyName(model.personName);
            DocRevTransmittal docRev = new DocRevTransmittal();

            //docRev.CreateNewDocRev();
            Sheets sheets = new Sheets();

            //List<string> revisionOrder = new List<string> { "A", "B", "C", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10" };

            foreach (var item in LoadedProject)
            {
                string theDateTime = DateTime.Now.ToString("dd MMM yyyy");

                //If the project exsists
                if (model.ProjectName == item.Name)
                {
                    exists = true;

                    var transID = transmittal.CreateNewTransmittal(model.TransmittalNo, model.No
                           , "0", "", theDateTime, _companyID.ToString(), _personID.ToString(), model.ProjectName, item.ProjectID);

                    int count = 0;

                    //It should not care if the sheet exists

                    if (transID != 0)
                    {
                        //foreach (var sheet in model.DocNo)
                        //{
                        for (int i = 0; i < model.DocNo.Count; i++)
                        {
                            var sheetloaded = sheets.LoadSingleSheet(model.DocNo[i]);

                            if (sheetloaded != null && sheetloaded.Id != 0 && !string.IsNullOrEmpty(sheetloaded.Name))
                            {
                                var enterNewDocRev = docRev.CreateNewDocRev(sheetloaded.Id.ToString(), sheetloaded.Name, model.Revision[i], theDateTime, transID.ToString(), item.Id.ToString());

                                if (enterNewDocRev != 0)
                                {

                                }
                            }
                            else
                            {
                                int newSheet = 0;
                                newSheet = sheets.InsertTheSheets(model.DocNo[i], model.Description[i]);

                                if (newSheet != 0)
                                {
                                    var docRevEntered = docRev.CreateNewDocRev(newSheet.ToString(), model.DocNo[i],
                                                                model.Revision[i], theDateTime, transID.ToString(), item.Id.ToString());

                                    if (docRevEntered > 0)
                                    {
                                        //enterd
                                    }
                                }
                            }
                        }

                        #region Old Method

                        //if (!string.IsNullOrEmpty(sheet))
                        //{
                        //    var sheetExist = sheets.LoadSingleSheet(sheet);

                        //    if (sheetExist != null && sheetExist.Id != 0 && !string.IsNullOrEmpty(sheetExist.Name))
                        //    {
                        //        HashSet<string> uniqueRevisions = new HashSet<string>();
                        //        var docTransmittal = docRev.ListAllWhereSheetID(sheetExist.Id.ToString());

                        //        if (docTransmittal.Count > 0)
                        //        {
                        //            //Get the largest revision                                    
                        //            foreach (var rev in docTransmittal)
                        //            {
                        //                uniqueRevisions.Add(rev.RevNo);
                        //            }

                        //            List<string> sortedRevisions = uniqueRevisions
                        //            .Where(rev => revisionOrder.Contains(rev))
                        //            .OrderBy(rev => revisionOrder.IndexOf(rev))
                        //            .ToList();

                        //            //Largest Rev if it equals Zero and its more than one 0 add 1
                        //            //or check if the rev is one
                        //            //or use the old method
                        //            string LargestRev = sortedRevisions.LastOrDefault();

                        //            var docRevEntered = docRev.CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                        //                 LargestRev, theDateTime, transID.ToString(), item.Id.ToString());

                        //            if (docRevEntered != 0)
                        //            {
                        //                //enterd
                        //            }
                        //        }
                        //        else
                        //        {
                        //            //Transmittal                                                                      
                        //            var docRevEntered = docRev.CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                        //                 "0", theDateTime, transID.ToString(), item.Id.ToString());

                        //            //CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                        //            //         LargestRev, theDateTime, transID.ToString(), item.Id.ToString());

                        //            if (docRevEntered > 0)
                        //            {
                        //                //enterd
                        //            }
                        //        }
                        //    }
                        //    else
                        //    {
                        //        int SheetID = 0;

                        //        if (!string.IsNullOrEmpty(model.Description[count]))
                        //        {
                        //            SheetID = sheets.InsertTheSheets(sheet, model.Description[count]);

                        //            //SheetID = InsertTheSheets(sheet, model.Description[count]);
                        //        }
                        //        else
                        //        {
                        //            SheetID = sheets.InsertTheSheets(sheet, "");

                        //            //SheetID = InsertTheSheets(sheet, "");
                        //        }

                        //        //add The sheet to database            
                        //        DocRevTransmittal docRev1 = new DocRevTransmittal();

                        //        Transmittal transmittal1 = new Transmittal();

                        //        //create the transmittal
                        //        string projectName1 = model.ProjectName;
                        //        string companyID = _companyID.ToString();

                        //        //Transmittal                                                                      
                        //        var docRevEntered = docRev.CreateNewDocRev(SheetID.ToString(), sheet,
                        //                                        "0", theDateTime, transID.ToString(), item.Id.ToString());

                        //        //CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                        //        //         LargestRev, theDateTime, transID.ToString(), item.Id.ToString());

                        //        if (docRevEntered > 0)
                        //        {
                        //            //enterd
                        //        }
                        //    }
                        //}

                        #endregion

                        count = count + 1;
                        //}
                    }
                }
            }

            //Method to save a new project 
            //If a project does not exist in the database you need to add it first
            if (!exists)
            {
                var results = project.Insert(model.ProjectName, model.projectID, "0");

                if (results)
                {
                    var LoadedProject1 = project.ListAll();

                    foreach (var item in LoadedProject1)
                    {
                        string theDateTime = DateTime.Now.ToString("dd MMM yyyy");

                        //If the project exsists
                        if (model.ProjectName == item.Name)
                        {
                            exists = true;

                            var transID = transmittal.CreateNewTransmittal(model.TransmittalNo, model.No
                                   , "0", "", theDateTime, _companyID.ToString(), _personID.ToString(), model.ProjectName, item.ProjectID);

                            int count = 0;

                            if (transID != 0)
                            {
                                //foreach (var sheet in model.DocNo)
                                //{

                                for (int Io = 0; Io < model.DocNo.Count; Io++)
                                {
                                    var sheetloaded = sheets.LoadSingleSheet(model.DocNo[Io]);

                                    if (sheetloaded != null && sheetloaded.Id != 0 && !string.IsNullOrEmpty(sheetloaded.Name))
                                    {
                                        var docRevEntered = docRev.CreateNewDocRev(sheetloaded.Id.ToString(), sheetloaded.Name, model.Revision[Io], theDateTime, transID.ToString(), item.Id.ToString());

                                        if (docRevEntered != 0)
                                        {
                                            //enterd
                                        }
                                    }
                                    else
                                    {
                                        int newSheetID = 0;

                                        newSheetID = sheets.InsertTheSheets(model.DocNo[Io], model.Description[Io]);

                                        if (newSheetID != 0)
                                        {
                                            var docRevInserted = docRev.CreateNewDocRev(newSheetID.ToString(), model.DocNo[Io], model.Revision[Io], theDateTime, transID.ToString(), item.Id.ToString());

                                            if (docRevInserted != 0)
                                            {
                                                //enterd
                                            }
                                        }
                                    }
                                }

                                #region OldMethod

                                //if (!string.IsNullOrEmpty(sheet))
                                //{
                                //    //
                                //    //Create DocRev
                                //    var sheetExist = sheets.LoadSingleSheet(sheet);

                                //    if (sheetExist != null && sheetExist.Id != 0 && !string.IsNullOrEmpty(sheetExist.Name))
                                //    {
                                //        HashSet<string> uniqueRevisions = new HashSet<string>();
                                //        var docTransmittal = docRev.ListAllWhereSheetID(sheetExist.Id.ToString());

                                //        if (docTransmittal.Count > 0)
                                //        {
                                //            //Get the largest revision                                    
                                //            foreach (var rev in docTransmittal)
                                //            {
                                //                uniqueRevisions.Add(rev.RevNo);
                                //            }

                                //            List<string> sortedRevisions = uniqueRevisions
                                //            .Where(rev => revisionOrder.Contains(rev))
                                //            .OrderBy(rev => revisionOrder.IndexOf(rev))
                                //            .ToList();

                                //            //Largest Rev if it equals Zero and its more than one 0 add 1
                                //            //or check if the rev is one
                                //            //or use the old method

                                //            string LargestRev = sortedRevisions.LastOrDefault();

                                //            switch (LargestRev)
                                //            {
                                //                case "A":
                                //                    LargestRev = "B";
                                //                    break;
                                //                case "B":
                                //                    LargestRev = "C";
                                //                    break;
                                //                case "C":
                                //                    LargestRev = "0";
                                //                    break;
                                //                case "0":
                                //                    LargestRev = "1";
                                //                    break;
                                //                case "1":
                                //                    LargestRev = "2";
                                //                    break;
                                //                case "2":
                                //                    LargestRev = "3";
                                //                    break;
                                //                case "3":
                                //                    LargestRev = "4";
                                //                    break;
                                //                case "4":
                                //                    LargestRev = "5";
                                //                    break;
                                //                case "5":
                                //                    LargestRev = "6";
                                //                    break;
                                //                case "6":
                                //                    LargestRev = "7";
                                //                    break;
                                //                case "7":
                                //                    LargestRev = "8";
                                //                    break;
                                //                case "8":
                                //                    LargestRev = "9";
                                //                    break;
                                //                case "9":
                                //                    LargestRev = "10";
                                //                    break;
                                //                case "10":
                                //                    LargestRev = "11";
                                //                    break;
                                //                case "11":
                                //                    LargestRev = "12";
                                //                    break;
                                //            }

                                //            var docRevEntered = docRev.CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                                //                 LargestRev, theDateTime, transID.ToString(), item.Id.ToString());

                                //            if (docRevEntered != 0)
                                //            {
                                //                //enterd
                                //            }
                                //        }
                                //        else
                                //        {
                                //            //Transmittal                                                                      
                                //            var docRevEntered = docRev.CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                                //                 "0", theDateTime, transID.ToString(), item.Id.ToString());

                                //            //CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                                //            //         LargestRev, theDateTime, transID.ToString(), item.Id.ToString());

                                //            if (docRevEntered > 0)
                                //            {
                                //                //enterd
                                //            }
                                //        }
                                //    }
                                //    else
                                //    {
                                //        int SheetID = 0;

                                //        if (!string.IsNullOrEmpty(model.Description[count]))
                                //        {
                                //            SheetID = sheets.InsertTheSheets(sheet, model.Description[count]);
                                //            //SheetID = InsertTheSheets(sheet, model.Description[count]);
                                //        }
                                //        else
                                //        {

                                //            SheetID = sheets.InsertTheSheets(sheet, "");
                                //            //SheetID = InsertTheSheets(sheet, "");
                                //        }

                                //        //add The sheet to database            
                                //        DocRevTransmittal docRev1 = new DocRevTransmittal();

                                //        Transmittal transmittal1 = new Transmittal();

                                //        //create the transmittal
                                //        string projectName1 = model.ProjectName;
                                //        string companyID = _companyID.ToString();

                                //        //Transmittal                                                                      
                                //        var docRevEntered = docRev.CreateNewDocRev(SheetID.ToString(), sheet,
                                //                                        "0", theDateTime, transID.ToString(), item.Id.ToString());

                                //        //CreateNewDocRev(sheetExist.Id.ToString(), sheetExist.Name,
                                //        //         LargestRev, theDateTime, transID.ToString(), item.Id.ToString());

                                //        if (docRevEntered > 0)
                                //        {
                                //            //enterd
                                //        }
                                //    }
                                //}

                                #endregion

                                count = count + 1;
                                //}
                            }
                        }
                    }
                }
            }

            string projectName = model.ProjectName; // Replace with actual project name
            string companyName = _companyID.ToString(); // Replace with actual company name or company ID

            // Redirect to the Index action of the DrawingRegister controller
            return RedirectToAction("Index", "DrawingRegister", new { projectName = projectName, companyName = companyName });

            //return RedirectToAction("Index", "DrawingRegister", new { model.ProjectName, _companyID });
        }

        [HttpPost]
        public ActionResult ExportPdf(TransmitallCreationDataViewModal1 model)
        {
            string projectNameToPlace = "";

            //Make sure the project name is not null
            if (!string.IsNullOrEmpty(model.ProjectName))
            {
                var ProjectNameSplit = model.ProjectName.Split('-');
                if (ProjectNameSplit.Length > 0)
                {
                    projectNameToPlace = ProjectNameSplit[0] + "-";
                }
            }

            // Define the path to save the PDF file in the "App_Data" directory
            string pdfFileName = projectNameToPlace + model.companyname + "-Transmittal Slip-" + DateTime.Now.ToString("M-d-yyyy") + ".pdf";
            string appDataPath = Server.MapPath("~/App_Data");
            string pdfFilePath = Path.Combine(appDataPath, pdfFileName);

            // Ensure the directory exists
            string directoryPath = Path.GetDirectoryName(pdfFilePath.Replace(pdfFileName, ""));
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            // Call the method to generate the PDF using QuestPDF
            HtmlContentHelper.QuestPDFTransmittalExport(model, pdfFilePath);

            // Read the PDF file into a byte array
            byte[] pdfBytes = System.IO.File.ReadAllBytes(pdfFilePath);

            // Return the PDF file
            return File(pdfBytes, "application/pdf", pdfFileName);
        }

        private async Task<List<dynamic>> GetPdfMetadata(string urn, Tokens tokens)
        {
            var derivativeApi = new DerivativesApi();
            var metadata = await derivativeApi.GetMetadataAsync(urn, null);

            var metadataId = metadata.Data.Metadata.First().MetadataId;
            var sheets = await derivativeApi.GetModelviewMetadataAsync(urn, metadataId, null);

            var sheetDetails = new List<dynamic>();
            foreach (var sheet in sheets.Data.Objects)
            {
                var details = new
                {
                    SheetName = sheet.Name,
                    Description = sheet.Description ?? "No description available",
                    Size = sheet.Size ?? "Unknown size"
                };
                sheetDetails.Add(details);
            }

            return sheetDetails;
        }

        string RemoveRev(string details)
        {
            return details.Replace("REV 0", "").Replace("REV 1", "").
                Replace("REV 2", "").Replace("REV 3", "")
                .Replace("REV 4", "").Replace("REV 5", "")
                .Replace("REV 6", "").Replace("REV 7", "")
                .Replace("REV 8", "").Replace("REV 9", "")
                .Replace("REV 10", "").Replace("REV 11", "")
                .Replace("REV00", "").Replace("REV01", "").
                Replace("REV02", "").Replace("REV03", "")
                .Replace("REV04", "").Replace("REV05", "")
                .Replace("REV06", "").Replace("REV07", "")
                .Replace("REV08", "").Replace("REV09", "")
                .Replace("REV10", "").Replace("REV11", "")
                .Replace("REV0", "").Replace("REV1", "").
                Replace("REV2", "").Replace("REV3", "")
                .Replace("REV4", "").Replace("REV5", "")
                .Replace("REV6", "").Replace("REV7", "")
                .Replace("REV8", "").Replace("REV9", "")
                .Replace("REV10", "").Replace("REV11", "");
        }
    }

    public class SheetTransmittalData
    {
        public string DocNo { get; set; }

        public string Description { get; set; }

        public object Revision { get; set; }

        public string SheetSize { get; set; }

        public string No { get; set; }

        public string TransmittalNo { get; set; }
    }
}