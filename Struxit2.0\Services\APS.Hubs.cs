﻿using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using Autodesk.Forge;
using Autodesk.Forge.Model;
using Newtonsoft.Json;
using System.Text;
using Autodesk.Forge.Client;
using System;
using Antlr.Runtime;
using Newtonsoft.Json.Linq;
using Struxit2._0.Model;
using System.Linq;
using System.Net;

namespace Struxit2._0.Services
{
    public partial class APS
    {
        public async Task<IEnumerable<dynamic>> GetHubs(Tokens tokens)
        {
            var hubs = new List<dynamic>();
            var api = new HubsApi();
            api.Configuration.AccessToken = tokens.InternalToken;
            var response = await api.GetHubsAsync();
            foreach (KeyValuePair<string, dynamic> hub in new DynamicDictionaryItems(response.data))
            {
                hubs.Add(hub.Value);
            }

            return hubs;
        }

        public async Task<IEnumerable<dynamic>> GetProjects(string hubId, Tokens tokens)
        {
            var projects = new List<dynamic>();
            var api = new ProjectsApi();
            api.Configuration.AccessToken = tokens.InternalToken;
            var response = await api.GetHubProjectsAsync(hubId);
            foreach (KeyValuePair<string, dynamic> project in new DynamicDictionaryItems(response.data))
            {
                projects.Add(project.Value);
            }

            return projects;
        }

        public async Task<IEnumerable<dynamic>> GetContents(string hubId, string projectId, string folderId,
            Tokens tokens)
        {
            var contents = new List<dynamic>();
            if (string.IsNullOrEmpty(folderId))
            {
                var api = new ProjectsApi();
                api.Configuration.AccessToken = tokens.InternalToken;

                var response = await api.GetProjectTopFoldersAsync(hubId, projectId);
                foreach (KeyValuePair<string, dynamic> folders in new DynamicDictionaryItems(response.data))
                {
                    contents.Add(folders.Value);
                }
            }
            else
            {
                var api = new FoldersApi();
                api.Configuration.AccessToken = tokens.InternalToken;

                #region oldMethod

                //var response = await api.GetFolderContentsAsync(projectId, folderId);
                //foreach (KeyValuePair<string, dynamic> item in new DynamicDictionaryItems(response.data))
                //{
                //    contents.Add(item.Value);
                //}

                #endregion


                #region NewMethod

                int offset = 0;
                int limit = 200; // Assuming the API allows a maximum of 200 items per page

                while (true)
                {
                    var response = await api.GetFolderContentsAsync(projectId, folderId, pageNumber: offset);
                    foreach (KeyValuePair<string, dynamic> item in new DynamicDictionaryItems(response.data))
                    {
                        contents.Add(item.Value);
                    }

                    var count = response.data.Count;

                    if (count < limit)
                    {
                        break; // No more items to fetch
                    }

                    //Next page
                    offset = offset + 1;
                }

                #endregion

                // Handle pagination
                //while (!string.IsNullOrEmpty(response.pagination?.next?.href))
                //{
                //    response = await api.GetFolderContentsAsync(projectId, folderId, response.pagination.next.href);
                //    contents.AddRange(response.data);
                //};
            }

            return contents;
        }

        public async Task<IEnumerable<dynamic>> GetVersions(string hubId, string projectId, string itemId,
            Tokens tokens)
        {
            var versions = new List<dynamic>();
            var api = new ItemsApi();
            api.Configuration.AccessToken = tokens.InternalToken;
            var response = await api.GetItemVersionsAsync(projectId, itemId);

            JObject json = JObject.Parse(response);
            string lineageUrn = json["data"][0]["relationships"]["item"]["data"]["id"].ToString();

            List<VersionDetails> versionDetailsList = new List<VersionDetails>();

            foreach (var version in json["data"])
            {
                VersionDetails details = new VersionDetails
                {
                    VersionUrn = version["id"].ToString(),
                    FileName = version["attributes"]["name"].ToString(),
                    VersionNumber = (int)version["attributes"]["versionNumber"],
                    LineageUrn = lineageUrn,
                    StorageSize = (long)version["attributes"]["storageSize"]
                };

                versionDetailsList.Add(details);
            }

            foreach (KeyValuePair<string, dynamic> version in new DynamicDictionaryItems(response.data))
            {
                versions.Add(version.Value);
            }

            return versions;
        }

        public async Task<List<VersionDetails>> GetVersions2(string hubId, string projectId, string itemId,
            Tokens tokens)
        {
            var versions = new List<dynamic>();
            var api = new ItemsApi();
            api.Configuration.AccessToken = tokens.InternalToken;
            var response = await api.GetItemVersionsAsync(projectId, itemId);

            JObject json = JObject.Parse(response.ToString());
            string lineageUrn = json["data"][0]["relationships"]["item"]["data"]["id"].ToString();

            List<VersionDetails> versionDetailsList = new List<VersionDetails>();

            foreach (var version in json["data"])
            {
                VersionDetails details = new VersionDetails
                {
                    ID = version["id"]?.ToString(),
                    VersionUrn = version["id"].ToString(),
                    FileName = version["attributes"]["name"].ToString(),
                    VersionNumber = (int)version["attributes"]["versionNumber"],
                    LineageUrn = lineageUrn,
                    StorageSize = (long)version["attributes"]["storageSize"]
                };

                versionDetailsList.Add(details);
            }

            return versionDetailsList;
        }

        public async Task<List<dynamic>> GetSheetMetadata(string urn, Tokens tokens)
        {
            try
            {
                var derivativesApi = new DerivativesApi();
                derivativesApi.Configuration.AccessToken = tokens.InternalToken;

                var metadata = await derivativesApi.GetMetadataAsync(urn, null);
                var metadataId = metadata.Data.Metadata.First().MetadataId;
                var sheets = await derivativesApi.GetModelviewMetadataAsync(urn, metadataId, null);

                var sheetDetails = new List<dynamic>();
                foreach (var sheet in sheets.Data.Objects)
                {
                    var details = new
                    {
                        SheetName = sheet.Name,
                        Description = sheet.Description ?? "No description available",
                        Size = sheet.Size ?? "Unknown size"
                    };
                    sheetDetails.Add(details);
                }

                return sheetDetails;
            }
            catch
            {
                return null;
            }
        }




        public async Task<dynamic> GetBatchVersionDetails(string projectId, List<string> versionIds, Tokens tokens)
        {
            // Initialize the API client for BIM 360 Docs
            var apiInstance = new VersionsApi();
            apiInstance.Configuration.AccessToken = tokens.InternalToken;

            try
            {
                // Make the batch-get request using the Forge SDK
                var response = await apiInstance.GetVersionAsync(projectId, versionIds[0]);     //  apiInstance.PostBatchGetAsync(projectId, new BatchVersionsRequest(versionIds));

                // Return the data directly from the SDK's response object
                return response.Data;
            }
            catch (ApiException e)
            {
                // Handle the exception and log the error
                Console.WriteLine("Exception when calling VersionsApi.PostBatchGetAsync: " + e.Message);
                return null;
            }
        }

        public async Task<dynamic> GetSingleVersionDetails(string projectId, string versionId, Tokens tokens)
        {
            var apiInstance = new VersionsApi();
            apiInstance.Configuration.AccessToken = tokens.InternalToken;

            try
            {
                // Make the request for a single version using the Forge SDK
                var response = await apiInstance.GetVersionAsync(projectId, versionId);

                // Return the data from the SDK's response object
                return response.Data;
            }
            catch (ApiException e)
            {
                Console.WriteLine($"Exception when calling VersionsApi.GetVersionAsync: {e.Message}");
                Console.WriteLine($"Status Code: {e.ErrorCode}");
                Console.WriteLine($"Response Body: {e.ErrorContent}");
                return null;
            }
        }

        public async Task<dynamic> GetVersionItems(string projectId, string versionId, Tokens tokens)
        {
            var apiInstance = new ItemsApi();
            apiInstance.Configuration.AccessToken = tokens.InternalToken;

            try
            {
                var response = await apiInstance.GetItemAsync(projectId, versionId);
                return response.Data;
            }
            catch (ApiException e)
            {
                Console.WriteLine("Exception when calling ItemsApi.GetItemAsync: " + e.Message);
                return null;
            }
        }


        public async Task<dynamic> GetVersionCustomAttributes(string projectId, string versionId, Tokens tokens)
        {
            // Initialize the Versions API
            var versionsApi = new VersionsApi();
            versionsApi.Configuration.AccessToken = tokens.InternalToken;

            try
            {
                // Retrieve custom attributes for the version
                var versionDetails = await versionsApi.GetVersionAsync(projectId, versionId);
                return versionDetails?.data?.attributes?.customAttributes;
            }
            catch (ApiException e)
            {
                // Handle API exception
                Console.WriteLine($"Error retrieving custom attributes: {e.Message}");
                return null;
            }
        }

        public async Task<dynamic> GetBatchVersionDetails1(string projectId, List<string> itemsIds, List<string> versionIds, Tokens tokens)
        {
            // Construct the endpoint URL
            string endpoint = $"https://developer.api.autodesk.com/bim360/docs/v1/projects/{projectId}/versions:batch-get";

            // Create the request body with the list of version IDs
            var requestBody = new
            {
                urns = itemsIds,
                versionIds
            };

            // Create the HTTP request
            var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
            {
                Content = new StringContent(JsonConvert.SerializeObject(requestBody), Encoding.UTF8, "application/json")
            };
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tokens.InternalToken);

            // Send the request
            var client = new HttpClient();
            var response = await client.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                // Parse the JSON response to a dynamic object
                var jsonResponse = await response.Content.ReadAsStringAsync();
                // return JsonConvert.DeserializeObject<dynamic>(jsonResponse).data;


                var parsedResponse = JsonConvert.DeserializeObject<dynamic>(jsonResponse);

                // Return the 'results' array, which contains the version details
                return parsedResponse.results;

            }
            else
            {
                // Handle the error response
                var errorResponse = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Error: {response.StatusCode}, Details: {errorResponse}");
                return null;
            }
        }

        public async Task<dynamic> GetBatchVersionDetails2(string projectId, string itemsIds, string versionIds, Tokens tokens)
        {
            // Construct the endpoint URL
            string endpoint = $"https://developer.api.autodesk.com/bim360/docs/v1/projects/{projectId}/versions:batch-get";

            // Combine item URNs and version URNs into a list of arrays (similar to JavaScript)
            var urnsList = new List<string>();

            // Each entry should be an array of item URN and version URN
            urnsList.Add(itemsIds);
            urnsList.Add(versionIds);

            // Create the request body with the combined list of URNs
            var requestBody = new
            {
                urns = urnsList
            };

            // Create the HTTP request
            var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
            {
                Content = new StringContent(JsonConvert.SerializeObject(requestBody), Encoding.UTF8, "application/json")
            };
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tokens.InternalToken);

            // Send the request
            var client = new HttpClient();
            var response = await client.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                // Parse the JSON response to a dynamic object
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var parsedResponse = JsonConvert.DeserializeObject<dynamic>(jsonResponse);

                // Return the 'results' array, which contains the version details
                return parsedResponse.results;
            }
            else
            {
                // Handle the error response
                var errorResponse = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Error: {response.StatusCode}, Details: {errorResponse}");
                return null;
            }
        }

        public async Task<dynamic> GetBatchVersionDetails3(string projectId, string itemsIds, string versionIds, Tokens tokens)
        {
            // Construct the endpoint URL
            string endpoint = $"https://developer.api.autodesk.com/bim360/docs/v1/projects/{projectId}/versions:batch-get";

            // The API expects an array of arrays, where each inner array contains [itemUrn, versionUrn]
            var urns = new List<string[]>
    {
        new[] { itemsIds, versionIds }
    };

            var requestBody = new { urns = urns };

            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokens.InternalToken);

                try
                {
                    var jsonContent = JsonConvert.SerializeObject(requestBody);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                    var response = await client.PostAsync(endpoint, content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        try
                        {
                            var parsedResponse = JsonConvert.DeserializeObject<dynamic>(responseContent);
                            if (parsedResponse.results == null)
                            {
                                throw new Exception("Response did not contain expected 'results' property");
                            }
                            return parsedResponse.results;
                        }
                        catch (JsonException ex)
                        {
                            throw new Exception($"Failed to parse response: {ex.Message}", ex);
                        }
                    }
                    else
                    {
                        // Handle specific error status codes
                        switch (response.StatusCode)
                        {
                            case HttpStatusCode.Unauthorized:
                                throw new UnauthorizedAccessException("Invalid or expired token");
                            case HttpStatusCode.BadRequest:
                                throw new Exception($"Bad request: {responseContent}");
                            case (HttpStatusCode)429: // Too Many Requests
                                throw new Exception("Rate limit exceeded");
                            default:
                                throw new Exception($"API request failed with status {response.StatusCode}: {responseContent}");
                        }
                    }
                }
                catch (HttpRequestException ex)
                {
                    throw new Exception($"HTTP request failed: {ex.Message}", ex);
                }
                catch (TaskCanceledException)
                {
                    throw new Exception("Request timed out");
                }
            }
        }


        public async Task<List<dynamic>> GetBatchVersionDetailsAsync(string projectId, List<string> itemsIds, List<string> versionIds, Tokens tokens)
        {
            // Construct a list to store the results
            var allResults = new List<dynamic>();

            // Create the HTTP client for sending the requests
            using (var client = new HttpClient())
            {

                // Construct the endpoint URL
                string endpoint = $"https://developer.api.autodesk.com/bim360/docs/v1/projects/{projectId}/versions:batch-get";

                // Create the request body with the URNs
                var requestBody = new
                {
                    urns = new[] { itemsIds, versionIds }
                };

                // Serialize the request body to JSON
                string jsonBody = JsonConvert.SerializeObject(requestBody);

                // Create the HTTP POST request
                var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
                {
                    Content = new StringContent(jsonBody, Encoding.UTF8, "application/json")
                };

                // Add the authorization header
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tokens.InternalToken);

                try
                {
                    // Send the request and get the response
                    HttpResponseMessage response = await client.SendAsync(request);

                    // Check if the request was successful
                    if (response.IsSuccessStatusCode)
                    {
                        // Parse the response content to dynamic
                        string jsonResponse = await response.Content.ReadAsStringAsync();
                        dynamic parsedResponse = JsonConvert.DeserializeObject<dynamic>(jsonResponse);

                        // Add the 'results' array to the allResults list
                        if (parsedResponse.results != null)
                        {
                            allResults.AddRange(parsedResponse.results);
                        }
                    }
                    else
                    {
                        // Log any errors
                        string errorResponse = await response.Content.ReadAsStringAsync();
                        Console.WriteLine($"Error: {response.StatusCode}, Details: {errorResponse}");
                    }
                }
                catch (HttpRequestException e)
                {
                    // Handle HTTP request errors (e.g., network issues)
                    Console.WriteLine($"Request error: {e.Message}");
                }
                catch (Exception ex)
                {
                    // Handle general exceptions
                    Console.WriteLine($"Unexpected error: {ex.Message}");
                }

            }

            // Return all the collected results
            return allResults;
        }


        public async Task<dynamic> GetItemDetails(string hubId, string projectId, string itemId, Tokens tokens)
        {
            string endpoint = $"https://developer.api.autodesk.com/data/v1/projects/{projectId}/items/{itemId}";
            var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tokens.InternalToken);

            var client = new HttpClient();
            var response = await client.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<dynamic>(jsonResponse);
            }
            return null;
        }

        public async Task<string> GetItemRevision(string hubId, string projectId, string itemId, Tokens tokens)
        {
            // Get the item details
            var itemDetails = await GetItemDetails(hubId, projectId, itemId, tokens);

            if (itemDetails != null && itemDetails.relationships.tip != null)
            {
                // Get the version ID from the item's tip
                string versionId = itemDetails.relationships.tip.data.id;

                // Use the version ID to get the version details
                var versionDetails = await GetItemVersionDetails(hubId, projectId, versionId, tokens);

                if (versionDetails != null)
                {
                    // Extract the revision label
                    string revision = versionDetails.attributes.extension.data.revisionDisplayLabel;

                    return revision;
                }
            }

            return "Revision not found";
        }

        public async Task<dynamic> GetItemVersionDetails(string hubId, string projectId, string versionId, Tokens tokens)
        {
            // Construct the endpoint for the version details
            string endpoint = $"https://developer.api.autodesk.com/data/v1/projects/{projectId}/versions/{versionId}";
            var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tokens.InternalToken);

            // Send the request to the Autodesk Forge API
            var client = new HttpClient();
            var response = await client.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                // Parse the JSON response to a dynamic object
                var jsonResponse = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<dynamic>(jsonResponse);
            }

            return null;
        }

        public async Task<dynamic> GetCustomAttributeDefinitions(string projectId, string folderId, Tokens tokens)
        {
            // Construct the endpoint URL for custom attribute definitions
            string endpoint = $"https://developer.api.autodesk.com/bim360/docs/v1/projects/{projectId}/folders/{folderId}/custom-attribute-definitions";

            // Create the HTTP request
            var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", tokens.InternalToken);

            // Send the request
            var client = new HttpClient();
            var response = await client.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                // Parse the JSON response to a dynamic object
                var jsonResponse = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<dynamic>(jsonResponse);
            }

            return null;
        }
    }
}
